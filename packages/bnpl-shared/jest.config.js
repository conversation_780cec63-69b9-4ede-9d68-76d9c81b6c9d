process.env.TZ = 'Asia/<PERSON>_<PERSON>_<PERSON>';

module.exports = {
  preset: 'react-native',
  setupFiles: ['<rootDir>/jest.setup.js'],
  moduleNameMapper: {
    '^react$': '<rootDir>/node_modules/react',
  },
  transform: { '\\.html$': 'jest-transform-stub' },
  transformIgnorePatterns: ['/node_modules/(?!victory|react-native-elements|react-native|react-navigation|@zpi/looknfeel-icons|@zpi/looknfeel/typhography|)'],
  coverageReporters: ['lcov', 'text', 'json-summary', 'cobertura'],
  collectCoverageFrom: ['src/**/*.{js,jsx,ts,tsx}'],
  testEnvironment: 'jsdom',
};
