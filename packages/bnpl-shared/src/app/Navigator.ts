import { <PERSON><PERSON>ey } from '../constants';
import AddInformationScreen from '../screens/onboarding-flow/AddInformationScreen';
import { HomeScreen, HomeUnbindScreen } from '../screens/HomeScreen';
import { LockOutScreen } from '../screens/LockOutScreen';
import { OldVersionLockOutScreen } from '../screens/OldVersionLockOutScreen';
import { OnboardingScreen } from '../screens/onboarding-flow/OnboardingScreen';
import SplashScreen from '../screens/SplashScreen';
import { Webview } from '../screens/Webview/Webview';
import { buildNavigator } from '../shared/nav/buildNavigator';
import ApplicationReview from '../screens/onboarding-flow/ApplicationReview';
import { Faq, ProductInfo } from 'bnpl-shared/src/screens/HelpCenter';
import { getLocationEnv } from '../shared/ZaloPayModules';
import { PersonalExtraDataScreen } from '../screens/onboarding-flow/PersonalExtraDataScreen';
import {
  ResubmitOnboardingScreen,
  RepaymentScreen,
  MaintenanceScreen,
  UserGuideScreen,
  TransactionDetailScreen,
  TransactionHistoryScreen,
  NotificationScreen,
  StatementDetailScreen,
  ErrorScreen,
  AccountScreen,
  MPAccountScreen,
  PaymentHistory,
  RepaymentHistory,
  MPRepaymentHistory,
  MPPaymentHistory,
  MPTransactionHistoryScreen,
  ReminderSettingScreen,
} from 'bnpl-shared/src/screens';
import { RenewFaceChallengeScreen } from 'bnpl-shared/src/features/renew_overdraft';
import MainScreen from 'bnpl-shared/src/screens/MainScreen';
import NonWhitelistOnboardingScreen from 'bnpl-shared/src/features/multipartner_integration/screens/NonWhitelistOnboardingScreen';
import {
  PartnerPickScreen,
  MPSubmitInfoDetailScreen,
  MPOnboardingScreen,
  MPVerifyOTPScreen,
  MPHomeScreen,
  MPMainScreen,
  MPRepayScreen,
  MPResubmitScreen,
  MPWaitingApprovalScreen,
} from 'bnpl-shared/src/features/multipartner_integration/screens';
import {
  MiniRepaymentHistoryScreen,
} from 'bnpl-shared/src/features/mini_bnpl/screens';
import EmbeddedRenewalInfoScreen from 'bnpl-shared/src/features/embedded/screens/RenewalInfoScreen';
import EmbeddedRenewSignScreen from 'bnpl-shared/src/features/embedded/screens/RenewSignScreen';
import EmbeddedRenewFaceAuthenScreen from 'bnpl-shared/src/features/embedded/screens/RenewFaceAuthenScreen';

const MultiPartnerRoutes = {
  [ScreenKey.PartnerPickScreen]: {
    component: PartnerPickScreen,
    title: 'Tài khoản trả sau',
  },
  [ScreenKey.MPSubmitDetailInfoScreen]: {
    component: MPSubmitInfoDetailScreen,
    title: 'Đăng ký thông tin',
  },
  [ScreenKey.MPOnboardingScreen]: {
    component: MPOnboardingScreen,
    header: null,
  },
  [ScreenKey.MiniRepaymentHistory]: {
    component: MiniRepaymentHistoryScreen,
    title: 'Khoản trả sau',
  },
  [ScreenKey.MPVerifyOTPScreen]: {
    component: MPVerifyOTPScreen,
    title: 'Tài khoản trả sau',
  },
  [ScreenKey.MPHomeScreen]: {
    component: MPHomeScreen,
    header: null,
  },
  [ScreenKey.MPMainScreen]: {
    ignoreObserver: true,
    component: MPMainScreen,
    title: 'Tài khoản trả sau',
  },
  [ScreenKey.MPRepayScreen]: {
    component: MPRepayScreen,
    header: null,
  },
  [ScreenKey.AccountScreen]: {
    component: MPAccountScreen,
    header: null,
  },
  [ScreenKey.MPResubmitScreen]: {
    component: MPResubmitScreen,
    title: 'Tài khoản trả sau',
  },
  [ScreenKey.MPAccountScreen]: {
    component: MPAccountScreen,
    title: 'Tài khoản trả sau',
  },
  [ScreenKey.MPPaymentHistory]: {
    component: MPPaymentHistory,
    header: null,
  },
  [ScreenKey.MPRepaymentHistory]: {
    component: MPRepaymentHistory,
    header: null,
  },
  [ScreenKey.MPTransactionHistory]: {
    component: MPTransactionHistoryScreen,
    ignoreObserver: true,
    title: 'Lịch sử giao dịch',
  },
  [ScreenKey.NonWhitelistOnboardingScreen]: {
    component: NonWhitelistOnboardingScreen,
    header: null,
  },
  [ScreenKey.MPWaitingApproval]: {
    component: MPWaitingApprovalScreen,
    header: null,
  },
  [ScreenKey.EmbeddedRenewalInfoScreen]: {
    component: EmbeddedRenewalInfoScreen,
    header: null,
  },
  [ScreenKey.EmbeddedRenewSignScreen]: {
    component: EmbeddedRenewSignScreen,
    header: null,
  },
  [ScreenKey.EmbeddedRenewFaceAuthenScreen]: {
    component: EmbeddedRenewFaceAuthenScreen,
    header: null,
  },
};

export const Navigator = buildNavigator(
  {
    ...MultiPartnerRoutes,
    [ScreenKey.LockOutScreen]: {
      component: LockOutScreen,
      header: null,
    },
    [ScreenKey.OldVersionLockOutScreen]: {
      component: OldVersionLockOutScreen,
      header: null,
    },
    [ScreenKey.SplashScreen]: {
      component: SplashScreen,
      header: null,
    },
    [ScreenKey.AddInformationScreen]: {
      component: AddInformationScreen,
      header: null,
    },
    [ScreenKey.HomeScreen]: {
      component: HomeScreen,
      header: null,
    },
    [ScreenKey.HomeUnbindScreen]: {
      component: HomeUnbindScreen,
      header: null,
    },
    [ScreenKey.OnboardingScreen]: {
      component: OnboardingScreen,
      header: null,
    },
    [ScreenKey.WebviewScreen]: {
      component: Webview,
      header: null,
    },
    [ScreenKey.ApplicationReview]: {
      component: ApplicationReview,
      header: null,
    },
    [ScreenKey.Faq]: {
      component: Faq,
      title: 'Câu hỏi thường gặp',
    },
    [ScreenKey.ProductInfo]: {
      component: ProductInfo,
      title: 'Thông tin sản phẩm',
    },
    [ScreenKey.TransactionHistory]: {
      component: TransactionHistoryScreen,
      ignoreObserver: true,
      title: 'Lịch sử giao dịch',
    },
    [ScreenKey.PaymentHistory]: {
      component: PaymentHistory,
      header: null,
    },
    [ScreenKey.RepaymentHistory]: {
      component: RepaymentHistory,
      header: null,
    },
    [ScreenKey.TransactionDetail]: {
      component: TransactionDetailScreen,
      title: 'Chi tiết giao dịch',
    },
    [ScreenKey.UserGuide]: {
      component: UserGuideScreen,
      title: 'Hướng dẫn sử dụng',
    },
    [ScreenKey.PersonalExtraDataScreen]: {
      component: PersonalExtraDataScreen,
      title: 'Thông tin cá nhân',
    },
    [ScreenKey.Maintenance]: {
      component: MaintenanceScreen,
      title: 'Tài khoản trả sau',
    },
    [ScreenKey.Repayment]: {
      component: RepaymentScreen,
      title: 'Tài khoản trả sau',
    },
    [ScreenKey.ResubmitOnboarding]: {
      component: ResubmitOnboardingScreen,
      header: null,
    },
    [ScreenKey.Notification]: {
      component: NotificationScreen,
      title: 'Thông báo',
    },
    [ScreenKey.Error]: {
      component: ErrorScreen,
      header: null,
    },
    [ScreenKey.RenewFaceChallenge]: {
      component: RenewFaceChallengeScreen,
      title: 'Tài khoản trả sau',
    },
    [ScreenKey.MainScreen]: {
      component: MainScreen,
      ignoreObserver: true,
      title: 'Tài khoản trả sau',
    },
    [ScreenKey.StatementDetailScreen]: {
      component: StatementDetailScreen,
      title: 'Chi tiết sao kê',
    },
    [ScreenKey.AccountScreen]: {
      component: AccountScreen,
      header: null,
    },
    [ScreenKey.ReminderSettingScreen]: {
      component: ReminderSettingScreen,
      header: null,
      title: 'Cài đặt nhắc hạn',
    },
  },
  {
    default: ScreenKey.SplashScreen,
    zpiBaseUrl: getLocationEnv().zpiBaseUrl,
  },
);
