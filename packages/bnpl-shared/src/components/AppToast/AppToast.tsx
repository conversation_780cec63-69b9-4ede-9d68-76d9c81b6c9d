import { ToastMessage } from 'bnpl-shared/src/shared/ToastMessage';
import { AppColors } from 'bnpl-shared/src/constants';
import React, { FC, useEffect } from 'react';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { setAppToast, ToastType } from 'bnpl-shared/src/redux/appToastReducer';
import { useDispatch } from 'react-redux';
import { StyleProp, ViewStyle } from 'react-native';
import { images } from 'bnpl-shared/src/res';
import { getNavigationBarHeight } from '../NavigationWrapper/context';

export type Prop = {
  style?: StyleProp<ViewStyle>;
};

export const AppToast: FC<Prop> = ({ style }) => {
  const dispatch = useDispatch();
  const toast = useAppSelector(state => state.appToast.toast);
  let icon: number | undefined;

  useEffect(() => {
    return () => {
      if (toast) {
        dispatch(setAppToast(null));
      }
    };
  }, [toast]);

  if (!toast) {
    return null;
  } else {
    if (toast.type === ToastType.SUCCESS) {
      icon = images.IconCheckCircle;
    } else if (toast.type === ToastType.ERROR) {
      icon = images.IconXCircle;
    }
    return (
      <ToastMessage
        style={[
          {
            backgroundColor: toast.type === ToastType.SUCCESS ? AppColors.expensePieArc : AppColors.transactionFailed,
            marginTop: getNavigationBarHeight() ?? 0,
          },
          style,
        ]}
        testId="app-toast"
        onHidden={() => dispatch(setAppToast(null))}
        icon={icon}
        timeout={toast.duration}
        content={toast.message}
      />
    );
  }
};
