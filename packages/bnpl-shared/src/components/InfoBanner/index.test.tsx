import React from 'react';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import * as spyRemoteConfigHook from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { NavigationContext } from 'bnpl-shared/src/utils/NavigationContext';
import { FAKE_NAVIGATION } from 'bnpl-shared/src/jest/constants';
import AsyncStorage from '@react-native-community/async-storage';
import { ExperimentName, TimeUnit } from 'bnpl-shared/src/types';
import { getDiffTime } from 'bnpl-shared/src/utils/getDiffTime';
import { launchInAppWebView } from 'bnpl-shared/src/shared/ZaloPayModules';
import * as spyABTestHook from 'bnpl-shared/src/hooks/useABTesting';
import { InfoBanner } from '.';
import { ConfigKey, RemoteConfigKey } from 'bnpl-shared/src/constants';

const mockGetConfig = jest.fn();

jest.spyOn(spyRemoteConfigHook, 'useRemoteConfigs').mockReturnValue({
  fetchConfigs: jest.fn(),
  getConfig: mockGetConfig,
  getConfigWithType: jest.fn(),
});

jest.mock('bnpl-shared/src/shared/ZaloPayModules', () => ({
  launchInAppWebView: jest.fn(),
}));

jest.mock('bnpl-shared/src/utils/getDiffTime');

jest.spyOn(spyABTestHook, 'useABTesting').mockReturnValue({
  fetchABTestResult: jest.fn(),
  isInWhiteList: jest.fn().mockReturnValue(true),
  isInWhiteListRealtime: jest.fn().mockReturnValue(true),
  getABTestResultByKey: jest.fn(),
});

describe('InfoBanner', () => {
  describe('verify render', () => {
    it('not render when config is not available', () => {
      const { queryByTestId } = renderWithRedux(<InfoBanner cacheKey={ConfigKey.CIMB_INFO_BANNER_TIMESTAMP_KEY} experimentName={ExperimentName.LANDING_PAGE} remoteConfigKey={RemoteConfigKey.INFO_BANNER} />);
      expect(queryByTestId('info-banner')).toBeFalsy();
    });

    it('when config is enabled and user have not close banner yet', async () => {
      mockGetConfig.mockImplementation(arg => {
        if (arg === 'info_banner') {
          return {
            enable: true,
            url: 'https://www.google.com',
            content: 'test_content',
          };
        }
      });
      const { queryByTestId, queryByText } = renderWithRedux(<InfoBanner cacheKey={ConfigKey.CIMB_INFO_BANNER_TIMESTAMP_KEY} experimentName={ExperimentName.LANDING_PAGE} remoteConfigKey={RemoteConfigKey.INFO_BANNER} />);
      await waitFor(() => expect(queryByTestId('info-banner')).toBeTruthy());
      await waitFor(() => expect(queryByText('test_content')).toBeTruthy());
    });

    it('when config is enabled and hidden time is not exceed hiddenTimeAmount', async () => {
      await AsyncStorage.setItem(ConfigKey.CIMB_INFO_BANNER_TIMESTAMP_KEY, '123456789');
      (getDiffTime as jest.Mock).mockReturnValue(23);
      mockGetConfig.mockImplementation(arg => {
        if (arg === 'info_banner') {
          return {
            enable: true,
            url: 'https://www.google.com',
            content: 'test_content',
          };
        }
      });
      const { queryByTestId } = renderWithRedux(<InfoBanner cacheKey={ConfigKey.CIMB_INFO_BANNER_TIMESTAMP_KEY} experimentName={ExperimentName.LANDING_PAGE} remoteConfigKey={RemoteConfigKey.INFO_BANNER} hiddenTimeAmount={24} hiddenTimeUnit={TimeUnit.HOURS} />);
      await waitFor(() => expect(queryByTestId('info-banner')).toBeFalsy());
    });

    it('when config is enabled and hidden time is exceed hiddenTimeAmount', async () => {
      await AsyncStorage.setItem(ConfigKey.CIMB_INFO_BANNER_TIMESTAMP_KEY, '123456789');
      (getDiffTime as jest.Mock).mockReturnValue(25);
      mockGetConfig.mockImplementation(arg => {
        if (arg === 'info_banner') {
          return {
            enable: true,
            url: 'https://www.google.com',
            content: 'test_content',
          };
        }
      });
      const { queryByTestId } = renderWithRedux(<InfoBanner cacheKey={ConfigKey.CIMB_INFO_BANNER_TIMESTAMP_KEY} experimentName={ExperimentName.LANDING_PAGE} remoteConfigKey={RemoteConfigKey.INFO_BANNER} hiddenTimeAmount={24} hiddenTimeUnit={TimeUnit.HOURS} />);
      await waitFor(() => expect(queryByTestId('info-banner')).toBeTruthy());
    });
  });

  describe('verify CTA is handle as expected', () => {
    beforeEach(() => {
      (getDiffTime as jest.Mock).mockClear();
      (getDiffTime as jest.Mock).mockReturnValue(25);
    });

    it('Tìm hiểu thêm', async () => {
      mockGetConfig.mockImplementation(arg => {
        if (arg === 'info_banner') {
          return {
            enable: true,
            url: 'https://www.google.com',
            content: 'test_content',
          };
        }
      });
      const { getByText, queryByTestId } = renderWithRedux(
        <NavigationContext.Provider value={FAKE_NAVIGATION}>
          <InfoBanner cacheKey={ConfigKey.CIMB_INFO_BANNER_TIMESTAMP_KEY} experimentName={ExperimentName.LANDING_PAGE} remoteConfigKey={RemoteConfigKey.INFO_BANNER} hiddenTimeAmount={24} hiddenTimeUnit={TimeUnit.HOURS} />
        </NavigationContext.Provider>,
      );
      await waitFor(() => expect(queryByTestId('info-banner')).toBeTruthy());
      fireEvent.press(getByText('Tìm hiểu thêm'));
      expect(launchInAppWebView).toHaveBeenCalledWith('https://www.google.com');
    });

    it('close button', async () => {
      mockGetConfig.mockImplementation(arg => {
        if (arg === 'info_banner') {
          return {
            enable: true,
            url: 'https://www.google.com',
            content: 'test_content',
          };
        }
      });
      const { getByTestId, queryByTestId } = renderWithRedux(<InfoBanner cacheKey={ConfigKey.CIMB_INFO_BANNER_TIMESTAMP_KEY} experimentName={ExperimentName.LANDING_PAGE} remoteConfigKey={RemoteConfigKey.INFO_BANNER} hiddenTimeAmount={24} hiddenTimeUnit={TimeUnit.HOURS} />);
      await waitFor(() => expect(queryByTestId('info-banner')).toBeTruthy());
      fireEvent.press(getByTestId('close-button'));
      expect(queryByTestId('info-banner')).toBeFalsy();
    });
  });
});
