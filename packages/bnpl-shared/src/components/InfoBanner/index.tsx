import React, { FC, useEffect, useState } from 'react';
import { useRemoteConfigs } from 'bnpl-shared/src/remote_configs/useRemoteConfigs';
import get from 'lodash/get';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { StyleProp, View, ViewStyle } from 'react-native';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { AppColors } from 'bnpl-shared/src/constants';
import { CloseButton } from 'bnpl-shared/src/shared/CloseButton';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import AsyncStorage from '@react-native-community/async-storage';
import { toNumber } from 'lodash';
import { getDiffTime } from 'bnpl-shared/src/utils/getDiffTime';
import { ExperimentName, TimeUnit } from 'bnpl-shared/src/types';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import { launchInAppWebView } from 'bnpl-shared/src/shared/ZaloPayModules';
import { useABTesting } from 'bnpl-shared/src/hooks/useABTesting';


export const InfoBanner: FC<{
  style?: StyleProp<ViewStyle>;
  hiddenTimeAmount?: number;
  hiddenTimeUnit?: TimeUnit;
  cacheKey: string;
  remoteConfigKey: string;
  experimentName: ExperimentName;
}> = ({
  style,
  hiddenTimeAmount = 24,
  hiddenTimeUnit = TimeUnit.HOURS,
  cacheKey,
  remoteConfigKey,
  experimentName,
}) => {
  const [isHidden, setIsHidden] = useState(true);
  const { isInWhiteListRealtime } = useABTesting();

  const { getConfig } = useRemoteConfigs();
  const configData = getConfig(remoteConfigKey);
  const content = get(configData, 'content');
  const enable = get(configData, 'enable');
  const url = get(configData, 'url');

  useEffect(() => {
    (async () => {
      const timestamp = await AsyncStorage.getItem(cacheKey);
      const isInfoBannerWhiteList = await isInWhiteListRealtime(experimentName);
      const shouldShowBanner = content && enable && isInfoBannerWhiteList;
      if (timestamp) {
        const timeSinceHidden = getDiffTime(toNumber(timestamp), new Date().getTime(), hiddenTimeUnit);
        if (timeSinceHidden > hiddenTimeAmount && shouldShowBanner) {
          setIsHidden(false);
        }
      } else {
        setIsHidden(!shouldShowBanner);
      }
    })();
  }, [hiddenTimeAmount, hiddenTimeUnit, cacheKey, experimentName]);

  if (isHidden || !experimentName || !remoteConfigKey) {
    return null;
  }

  return (
    <View testID="info-banner" style={[styles.root, style]}>
      <View style={styles.row}>
        <AppText style={StyleUtils.flexOne}>{content}</AppText>
        <CloseButton
          onPress={async () => {
            setIsHidden(true);
            await AsyncStorage.setItem(cacheKey, new Date().getTime().toString());
          }}
          size={16}
          tintColor={AppColors.disabled}
          style={styles.closeButton}
        />
      </View>
      <LinkButton
        onPress={() => {
          launchInAppWebView(avoidCacheImageUrl(url));
        }}>
        Tìm hiểu thêm
      </LinkButton>
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    backgroundColor: AppColors.primary2,
    borderRadius: 8,
    paddingVertical: 10,
    paddingStart: 16,
  },
  row: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'flex-start',
  },
  closeButton: {
    paddingHorizontal: 16,
  },
});
