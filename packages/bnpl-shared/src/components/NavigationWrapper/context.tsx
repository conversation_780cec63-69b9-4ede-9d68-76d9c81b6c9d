import { useEffect, useState } from 'react';
import { ScrollMaskConfig } from './types';
import { AppColors, ScreenKey } from 'bnpl-shared/src/constants';
import { createSubject, Subject } from 'bnpl-shared/src/utils/observer';

const INITIAL_NAVIGATION_OPTIONS = {
  [ScreenKey.MiniRepaymentHistory]: {
    background: "white",
    color: AppColors.dark[500],
  },
};

export const fullScreenSubject = createSubject<{ fullScreen: boolean, navigationBarHeight: number }>({ fullScreen: false, navigationBarHeight: 0 });
export const navigationBackableSubject = createSubject<boolean>(false);
export const navigationTitleSubject = createSubject<string>('Tài khoản trả sau');
export const navigationOptionsSubject = createSubject<Partial<Record<string, CustomNavigationOptions>>>(INITIAL_NAVIGATION_OPTIONS);

const DEFAULT_NAVIGATION_OPTIONS = {
  background: AppColors.blue[500],
  color: AppColors.white,
}
export function useWatchNavigation<T>(subject: Subject<T>) {
  const [value, setValue] = useState<T>(subject.state);
  useEffect(() => {
    const subscription = subject.subscribe({ next: setValue });
    return () => subscription.unsubscribe();
  }, []);
  return value;
}

export function isFullScreen() {
  return fullScreenSubject.state.fullScreen;
}

export function getNavigationBarHeight() {
  return fullScreenSubject.state.navigationBarHeight;
}

export function setNavigationTitle(title: string) {
  document.title = title;
  navigationTitleSubject.next(title);
}

export function setCustomNavigationOptions(path: string, options: CustomNavigationOptions) {
  navigationOptionsSubject.next({
    ...navigationOptionsSubject.state,
    [path]: options,
  });
}

const initialState: NavigationState = {
  navigationOptions: {
    background: AppColors.blue[500],
    color: AppColors.white,
  },
  customNavigationOptions: {},
};

type NavigationAction =
  | {
    type: 'SET_CUSTOM_NAVIGATION_OPTIONS';
    payload: {
      path: string;
      options: CustomNavigationOptions;
    };
  }

interface NavigationState {
  navigationOptions?: DefaultNavigationOptions;
  customNavigationOptions: Partial<Record<string, CustomNavigationOptions>>;
}

export interface DefaultNavigationOptions {
  className?: string;
  background?: string;
  color?: string;
  isWhiteStatusBar?: boolean;
}

export interface CustomNavigationOptions extends DefaultNavigationOptions {
  noNavigationBar?: boolean;
  overlappingPageContent?: boolean;
  scrollMask?: ScrollMaskConfig & {
    enable?: boolean;
    fullHeightNavigationBar?: boolean;
  };
}

// Reducer function
function navigationReducer(state: NavigationState, action: NavigationAction): NavigationState {
  switch (action.type) {
    case 'SET_CUSTOM_NAVIGATION_OPTIONS': {
      const { payload } = action;
      return {
        ...state,
        customNavigationOptions: {
          ...state.customNavigationOptions,
          [payload.path]: {
            ...state.customNavigationOptions[payload.path],
            ...payload.options,
          },
        },
      };
    }
    default:
      return state;
  }
}

export const useCurrentNavigationOptions = (pathName: string): (DefaultNavigationOptions & CustomNavigationOptions) => {
  const customNavigationOptions = useWatchNavigation(navigationOptionsSubject);
  const customOptionKey = Object.keys(customNavigationOptions).find(key => {
    return pathName.includes(key);
  });
  if (customOptionKey && customNavigationOptions[customOptionKey]) {
    return {
      ...DEFAULT_NAVIGATION_OPTIONS,
      ...customNavigationOptions[customOptionKey],
    };
  }
  return DEFAULT_NAVIGATION_OPTIONS;
};
