import React from 'react';
import { NavigationBar } from './NavigationBar';
import { ReactNode, useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import {
  useCurrentNavigationOptions,
  navigationBackableSubject,
  fullScreenSubject,
  navigationTitleSubject,
  navigationOptionsSubject,
} from './context';
import { useHistory, useLocation } from 'react-router-dom';
import { ScrollMask } from './ScrollMask';
import useFullScreen from "bnpl-shared/src/hooks/useFullScreen";
import { View } from 'react-native';
import { toast } from '../Toaster';
import { calculateScaleDimension } from 'bnpl-shared/src/shared/styles/StyleSheet';

export {
  isFullScreen,
  setCustomNavigationOptions,
  getNavigationBarHeight,
  useWatchNavigation,
  setNavigationTitle,
} from './context';

export function NavigationWrapper({ className, contentClassName, children }: NavigationWrapperProps) {
  const { pathname } = useLocation();
  const fullScreenData = useFullScreen();
  const history = useHistory();
  const stackRef = useRef<string[]>([]);
  const scrollRef = useRef<HTMLDivElement | null>(null);
  const [maskOpacity, setMaskOpacity] = useState(0);
  const currentNavigationOptions = useCurrentNavigationOptions(pathname);
  const availableFullScreen = fullScreenData?.fullScreen;
  const showNavigationBar = !currentNavigationOptions.noNavigationBar && availableFullScreen;
  const showScrollMask = currentNavigationOptions?.scrollMask?.enable && availableFullScreen;
  const navigationBarOverlappingPageContent = currentNavigationOptions?.overlappingPageContent || !showNavigationBar;

  useEffect(() => {
    return () => {
      fullScreenSubject.unsubscribe();
      navigationBackableSubject.unsubscribe();
      navigationTitleSubject.unsubscribe();
      navigationOptionsSubject.unsubscribe();
    }
  }, []);

  useEffect(() => {
    const unlisten = history.listen((location, action) => {
      if (!location.key) return; // fallback: ignore

      if (action === 'PUSH') {
        stackRef.current.push(location.key);
      } else if (action === 'POP') {
        const idx = stackRef.current.lastIndexOf(location.key);
        if (idx !== -1) {
          stackRef.current = stackRef.current.slice(0, idx + 1);
        } else {
          stackRef.current = [location.key];
        }
      } else if (action === 'REPLACE') {
        if (stackRef.current.length === 0) {
          stackRef.current = [location.key];
        } else {
          stackRef.current[stackRef.current.length - 1] = location.key;
        }
      }
      navigationBackableSubject.next(stackRef.current.length > 1);
    });

    return () => unlisten();
  }, []);

  useEffect(() => {
    (window as any)?.zlpSdk?.UI.changeStatusBarTextColor({
      isWhiteText: currentNavigationOptions?.isWhiteStatusBar ?? true,
    });
  }, [currentNavigationOptions?.isWhiteStatusBar]);

  useEffect(() => {
    if (fullScreenData) {
      fullScreenSubject.next({
        fullScreen: fullScreenData.fullScreen,
        navigationBarHeight: fullScreenData.navigationBarHeight,
      });
      toast.setNavigationBarHeight(calculateScaleDimension(fullScreenData?.navigationBarHeight ?? 0));
    }
  }, [fullScreenData]);

  function handleScroll() {
    if (!scrollRef.current) return;
    if (scrollRef.current.scrollTop > 15) {
      setMaskOpacity(1);
      return;
    }
    setMaskOpacity(0);
  }

  return (
    <Wrapper className={className}>
      {showNavigationBar && fullScreenData.navigationBarHeight && (
        <NavigationBar
          {...currentNavigationOptions}
          height={fullScreenData.navigationBarHeight}
        />
      )}
      <div style={{ height: '100%', overflow: 'hidden' }}>
        <Content ref={scrollRef} onScroll={handleScroll} className={contentClassName}>
          {showScrollMask && (
            <ScrollMask
              {...currentNavigationOptions?.scrollMask}
              opacity={maskOpacity}
              height={
                currentNavigationOptions?.scrollMask?.fullHeightNavigationBar
                  ? fullScreenData.navigationBarHeight
                  : currentNavigationOptions?.scrollMask?.height
              }
            />
          )}
          {!navigationBarOverlappingPageContent ? (
            <>
              <View style={{ height: fullScreenData.navigationBarHeight ?? 0, flexShrink: 0 }} />
              {children}
            </>
          ) : (
            children
          )}
        </Content>
      </div>
    </Wrapper>
  );
}

interface NavigationWrapperProps {
  className?: string;
  contentClassName?: string;
  navigationLeftButton?: {
    icon?: ReactNode;
    onClick?: () => void;
  };
  children?: ReactNode;
}

const Wrapper = styled.div`
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: 100%;
  overscroll-behavior-y: none;
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
`;
