import React, { useRef, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { calculateScaleDimension, StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';
import { AppText, AppImage } from 'bnpl-shared/src/shared/react-native-customized';
import { SwiperComponent, SwiperComponentRef } from 'bnpl-shared/src/shared/swiper';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { images } from 'bnpl-shared/src/res';
import { windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';

export default function ImageSlider({ slides }: ImageSliderProps) {
  const swiperRef = useRef<SwiperComponentRef>(null);
  const [activeIndex, setActiveIndex] = useState(0);

  return (
    <View style={styles.container}>
      <View style={styles.sliderWrapper}>
        <SwiperComponent
          suppressTitleWarning
          showsPagination={false}
          scrollEnabled
          slidesPerView={1}
          ref={swiperRef}
          onActiveIndexChange={setActiveIndex}
          loop>
          {slides.map((slide) => (
            <View key={slide.text} style={[styles.slide, { width: windowWidth - calculateScaleDimension(48) }]}>
              <View style={styles.titleWrapper}>
                <AppText bold color={AppColors.primary} style={styles.title} size={14} height={18}>{slide.text}</AppText>
              </View>
              <Spacer height={12} />
              <AppImage source={slide.image} height={391} width={218} />
            </View>
          ))}
        </SwiperComponent>
      </View>
      {activeIndex > 0 && <TouchableOpacity style={styles.backButton} onPress={() => swiperRef.current?.slidePrev()}>
        <AppImage source={images.MiniBNPLSliderBack} height={24} width={24} />
      </TouchableOpacity>}
      {activeIndex < slides.length - 1 && <TouchableOpacity style={styles.nextButton} onPress={() => swiperRef.current?.slideNext()}>
        <AppImage source={images.MiniBNPLSliderNext} height={24} width={24} />
      </TouchableOpacity>}
      <Spacer height={12} />
      <AppText color={AppColors.dark[300]} size={14} height={18} style={styles.indicator}><AppText bold color={AppColors.primary} size={14} height={18}>{activeIndex + 1}</AppText>/{slides.length}</AppText>
    </View>
  );
};

interface ImageSliderProps {
  slides: {
    image: string;
    text: string;
  }[]
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F9FF',
    borderRadius: 16,
    overflow: 'hidden',
    paddingVertical: 16,
    position: 'relative',
  },
  sliderWrapper: {
    overflow: 'visible',
  },
  slide: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleWrapper: {
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    textAlign: 'center',
  },
  indicator: {
    textAlign: 'center',
  },
  backButton: {
    position: 'absolute',
    left: 8,
    top: '45%',
  },
  nextButton: {
    position: 'absolute',
    right: 8,
    top: '45%',
  }
});

