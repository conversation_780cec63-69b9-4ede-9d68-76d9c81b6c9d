import React from 'react';
import { View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import SuggestedServicesItem from './SuggestedServicesItem';
import { launchDeeplink } from 'bnpl-shared/src/lib/ZalopaySDK/launchDeeplink';
import { images } from 'bnpl-shared/src/res';

export default function SuggestedServices({ trackEventClickItem }: SuggestedServicesProps) {
  return (
    <View style={styles.container}>
      {services.map(service => (
        <SuggestedServicesItem
          key={service.tag}
          bordered
          imageURL={service.imageURL}
          title={service.title}
          onClick={() => {
            trackEventClickItem?.(service.tag);
            launchDeeplink({
              zpa: service.zpa,
              zpi: service.zpi
            });
          }}
        />
      ))}
      <SuggestedServicesItem lastItem imageURL={images.ServiceComingSoon.uri} title={`Và nhiều dịch vụ${'\n'}kh<PERSON>c sắp ra mắt`} onClick={() => { }} />
    </View>
  )
}

interface SuggestedServicesProps {
  trackEventClickItem?: (tag: string) => void;
}

const services = [
  {
    imageURL: images.ServiceTelcoTopup.uri,
    title: `Nạp tiền${'\n'}điện thoại`,
    tag: 'telco_topup',
    zpa: 'zalopay://launch/app/61',
    zpi: (window.__BASE_NAME__ || '/spa/v2') + '/telco/topup',
  }, {
    imageURL: images.ServiceEntertainmentMovie.uri,
    title: `Đặt vé${'\n'}xem phim`,
    tag: 'movie',
    zpa: 'zalopay://launch/app/19',
    zpi: (window.__BASE_NAME__ || '/spa/v2') + '/movie',
  }
]

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'nowrap',
  }
})
