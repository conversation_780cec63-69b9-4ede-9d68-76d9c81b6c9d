import React, { useEffect } from 'react';
import { isFullScreen, setCustomNavigationOptions } from 'bnpl-shared/src/components/NavigationWrapper';
import { PartnerCode, ScreenKey } from 'bnpl-shared/src/constants';
import LinearGradient from 'react-native-linear-gradient';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { View } from 'react-native';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import SectionHeader from '../../components/SectionHeader';
import SuggestedServices from '../../components/SuggestedServices';
import { useHomePreviewController } from 'bnpl-shared/src/utils/useHomePreviewController';
import HelpOptions from './components/HelpOptions';
import BalanceSummary from './components/BalanceSummary';
import BuyTutorialButton from './components/BuyTutorialButton';
import * as Tracking from './tracking';
import { RepaymentWidget } from 'bnpl-shared/src/components/RepaymentWidget';
import ProviderStatement from '../../components/ProviderStatement';
import { NavigationScreenProps } from 'react-navigation';

export default function MiniBNPLHomeScreenView(props: NavigationScreenProps) {
  const { navigation } = props;
  const { isPreviewMode } = useHomePreviewController(PartnerCode.MINI_BNPL);
  useEffect(() => {
    Tracking.load({ accountState: 'active' });
    setCustomNavigationOptions(ScreenKey.MPHomeScreen, {
      background: 'transparent',
      color: 'black',
      overlappingPageContent: true,
      scrollMask: {
      enable: true,
        background: 'white',
        fullHeightNavigationBar: true,
      },
    });
  }, []);

  return (
    <View style={styles.container}>
      <LinearGradient style={[styles.headerGradient, !isFullScreen() && styles.headerGradientNonFullScreen]} colors={['rgba(213, 253, 237, 1)', 'rgba(213, 253, 237, 0)']} />
      <View style={[styles.paddingHorizontal, styles.balanceSummaryWrapper]}>
        <BalanceSummary isPreviewMode={isPreviewMode} />
        <Spacer height={12} />
      </View>
      <View style={styles.paddingHorizontal}>
        <SectionHeader
          title="Quản lý tài khoản trả sau"
          cta={{
            label: 'Xem tất cả',
            onClick: () => {
              Tracking.clickSeeAllBills({ accountState: 'active' });
              navigation.navigate(ScreenKey.MiniRepaymentHistory);
            }
          }}
        />
        <Spacer height={12} />
        <RepaymentWidget partnerCode={PartnerCode.MINI_BNPL} isPreviewMode={isPreviewMode} />
      </View>
      <View style={styles.paddingHorizontal}>
        <SectionHeader title="Dịch vụ hỗ trợ thanh toán" />
        <Spacer height={12} />
        <SuggestedServices
          trackEventClickItem={(tag) => {
            Tracking.clickSuggestedService({ iconName: tag });
          }}
        />
        <Spacer height={11} />
        <BuyTutorialButton />
      </View>
      <Spacer height={11} />
      <View style={styles.paddingHorizontal}>
        <SectionHeader title="Bạn cần trợ giúp?" />
        <Spacer height={12} />
        <HelpOptions />
      </View>
      <ProviderStatement />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
  },
  balanceSummaryWrapper: {
    marginTop: -118,
  },
  paddingHorizontal: {
    paddingHorizontal: 16,
  },
  headerGradient: {
    width: 375,
    height: 234,
  },
  headerGradientNonFullScreen: {
    marginTop: -104,
  },
});
