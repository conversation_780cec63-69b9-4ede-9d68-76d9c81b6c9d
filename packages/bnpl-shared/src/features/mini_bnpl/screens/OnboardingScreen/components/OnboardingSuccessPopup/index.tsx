import React, { lazy } from 'react';
import { AppButton, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { View } from 'react-native';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { useUserBalanceController } from 'bnpl-shared/src/utils/useUserBalanceController';
import { Skeleton } from 'bnpl-shared/src/shared/animated-wrappers/Skeleton';
import { ResourceState } from 'bnpl-shared/src/types';
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';

const ANIMATION_URL = 'https://simg.zalopay.com.vn/fs/bnpl/animation/mini-bnpl/onboarding_success.riv';
const Rive = lazy(() => import('@rive-app/react-canvas'));

export default function OnboardingSuccessPopup({ onCTAClick }: OnboardingSuccessPopupProps) {
  const { balanceSummary } = useUserBalanceController();
  return (
    <View style={styles.container}>
      <View style={styles.animationImage}>
        <Rive stateMachines="State Machine 1" src={ANIMATION_URL} width="100%" height="auto" />
      </View>
      <View style={styles.mainContent}>
        {balanceSummary.state !== ResourceState.READY ? (
          <Skeleton width={100} height={36} />
        ) : (
          <AppText style={styles.text} size={20} height={36} bold color={AppColors.white}>
            Nhận hạn mức {formatCurrency(balanceSummary.data?.total_limit)}
          </AppText>
        )}
        <Spacer height={4} />
        <AppText style={styles.text} size={16} height={20} color={AppColors.white}>
          Chúc mừng bạn đã nhận được hạn mức Tài khoản trả sau. Sử dụng để chi tiêu ngay khi cần bạn nhé.
        </AppText>
        <Spacer height={16} />
        <AppButton onPress={onCTAClick} title="Đã hiểu" buttonStyle={styles.button} />
      </View>
    </View>
  );
}

interface OnboardingSuccessPopupProps {
  onCTAClick: () => void;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  animationImage: {
    width: 375,
    height: 300,
    overflow: 'hidden',
  },
  mainContent: {
    paddingHorizontal: 16,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    textAlign: 'center',
  },
  button: {
    width: '100%',
  },
});
