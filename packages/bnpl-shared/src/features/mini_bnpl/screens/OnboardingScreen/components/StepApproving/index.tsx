import React, { useEffect } from 'react';
import { View } from 'react-native';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import BottomButton from 'bnpl-shared/src/shared/BottomButton';
import LinearGradient from 'react-native-linear-gradient';
import { isFullScreen } from 'bnpl-shared/src/components/NavigationWrapper';
import { themes } from './config';
import * as Tracking from '../../tracking';
import { useMiniBnplOnboardingScreenContext } from '../../context';
import { MiniBNPLOnboardingNextStep } from 'bnpl-shared/src/types';
import { getOnboardingStatusApi } from 'bnpl-shared/src/api/mini-bnpl';
import { launchDeeplink } from 'bnpl-shared/src/lib/ZalopaySDK/launchDeeplink';

export default function StepApproving() {
  const { currentStep, verifyStep } = useMiniBnplOnboardingScreenContext();
  const uiConfig = themes[MiniBNPLOnboardingNextStep.QUERY_STATUS];

  function handleCTAClick() {
    Tracking.clickWaitingApprovalCTA({ currentStep: currentStep });
    launchDeeplink({
      zpi: window.__BASE_NAME__ || '/spa/v2',
      zpa: 'zalopay://launch/tab/home',
    });
  }

  useEffect(() => {
    if (currentStep !== MiniBNPLOnboardingNextStep.QUERY_STATUS) {
      return;
    }
    const interval = setInterval(() => {
      getOnboardingStatusApi().then((res) => {
        verifyStep(res.onboarding_next_step as MiniBNPLOnboardingNextStep);
      });
    }, 3000);

    return () => clearInterval(interval);
  }, [currentStep]);

  return (
    <View style={[styles.container, isFullScreen() && styles.fullScreen]}>
      <LinearGradient colors={['#FFFAE9', '#FFF1D0']} style={styles.gradient} />
      <AppImage
        style={styles.backgroundImage}
        source={images.BackgroundMiniBNPLOnboardingStepApproving}
        width={375}
        height={812}
      />
      <AppImage source={uiConfig.lottieAnimation} {...uiConfig.animationStyle} />
      <View style={styles.body}>
        <AppText size={16} height={20} style={styles.title} bold>
          {uiConfig.title}
        </AppText>
        <AppText size={14} height={22} style={styles.description}>
          {uiConfig.descriptions}
        </AppText>
      </View>
      <BottomButton style={styles.cta} title="Màn hình chính" onPress={handleCTAClick} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 156,
    alignItems: 'center',
    height: '100%',
    position: 'relative',
  },
  fullScreen: {
    paddingBottom: 8,
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backgroundImage: {
    top: 0,
    right: 0,
    left: 0,
    position: 'absolute',
  },
  body: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    textAlign: 'center',
    marginTop: 16,
  },
  description: {
    textAlign: 'center',
    marginTop: 12,
  },
  cta: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
  },
});
