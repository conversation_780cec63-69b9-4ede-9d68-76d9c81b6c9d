import React from 'react';
import { View, ScrollView } from 'react-native';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { images } from 'bnpl-shared/src/res';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { AppColors } from 'bnpl-shared/src/constants';
import BottomButton from 'bnpl-shared/src/shared/BottomButton';
import { isFullScreen } from 'bnpl-shared/src/components/NavigationWrapper';
import { useMiniBnplOnboardingScreenContext } from '../../context';
import * as Tracking from '../../tracking';
import { hideLoading, showLoading, usePromptAuthChallengeFlow } from 'bnpl-shared/src/shared/ZaloPayModules';
import { AuthChallengeType, UMStatus } from 'bnpl-shared/src/types';
import { useAuthChallengeSource } from 'bnpl-shared/src/hooks/useAuthChallengeSource';
import { verifyFaceChallengeApi } from 'bnpl-shared/src/api/mini-bnpl/verifyFaceAuthen';
import { toast } from 'bnpl-shared/src/components/Toaster';

export default function StepRegister() {
  const { verifyStep } = useMiniBnplOnboardingScreenContext();
  const authSource = useAuthChallengeSource();
  const { promptAuthChallengeFlow } = usePromptAuthChallengeFlow();
  return (
    <View style={[styles.container, isFullScreen() && styles.fullScreen]}>
      <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView} contentContainerStyle={styles.scrollViewContent}>
        <AppImage style={styles.avatar} source={images.MiniBNPLFaceChallengeAvatar} width={180} height={240} />
        <AppText style={styles.title}>Chụp ảnh chân dung của bạn để xác minh</AppText>
      </ScrollView>
      <View style={styles.bottomContainer}>
        <BottomButton title="Chụp ảnh" onPress={async () => {
          Tracking.clickCTATakePicture();
          try {
            const result = await promptAuthChallengeFlow(Number(authSource), AuthChallengeType.FaceAuth);
            if (result.status === UMStatus.Success && result.resultData) {
              showLoading();
              // WIP: update data
              verifyFaceChallengeApi({ um_request_id: result.resultData }).then((res) => {
                verifyStep(res.onboarding_next_step);
              }).catch((err) => {
                console.log("verify face challenge error", err);
                toast.error(err?.message || 'Có lỗi xảy ra, vui lòng thử lại sau.', { duration: 3000 });
              });
            }
          } catch (e: any) {
            if (e !== 'failed') {
              toast.error(e?.message || 'Có lỗi xảy ra, vui lòng thử lại sau.', { duration: 3000 });
            }
          } finally {
            hideLoading();
          }
        }} />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: AppColors.white,
    alignItems: 'center',
    height: '100%',
  },
  fullScreen: {
    paddingBottom: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    width: '100%',
    alignItems: 'center',
    paddingTop: '60%',
    paddingBottom: 80,
  },
  avatar: {
    width: 180,
    height: 240,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: AppColors.white,
  },
  title: {
    textAlign: 'center',
    marginTop: 36,
  },
  tutorial: {
    textAlign: 'center',
    marginTop: 36,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: AppColors.white,
  },
});
