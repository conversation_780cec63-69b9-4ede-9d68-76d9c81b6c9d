import React, { useState, useMemo, useEffect } from 'react';
import styled from 'styled-components';
import Tabs from 'bnpl-shared/src/components/Tabs';
import { RepaymentListSkeleton, PaidRepaymentList, UnpaidRepaymentList } from "./components/RepaymentList";
import { useLocation } from "react-router-dom";
import useRepaymentHistory from "./useRepaymentHistory";
import { MiniRepaymentHistoryContext } from './context';

export default function MiniRepaymentHistoryScreen() {
  const { search } = useLocation();
  const searchParams = useMemo(() => new URLSearchParams(search), [search]);
  const defaultTab = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState<TabKey>(defaultTab === "paid" ? "paid" : "unpaid");
  const {submitRepay, history} = useRepaymentHistory();

  const handleTabChange = (tab: string) => {
    setActiveTab(tab as TabKey);
  };

  function renderContent() {
    if (!history) {
      return (
        <RepaymentListSkeleton />
      )
    }
    const {
      overduePayments,
      upcomingPayments,
      paidPayments,
    } = history;
    if (activeTab === 'unpaid') {
      return (
        <UnpaidRepaymentList
            onRequestRepayItem={(item) => {
              submitRepay(item.billId)
            }}
          emptyPaid={!Array.isArray(paidPayments) || paidPayments.length === 0}
          overduePayments={overduePayments}
          upcomingPayments={upcomingPayments}
        />
      );
    }
    return (
      <PaidRepaymentList
          onRequestRepayItem={(item) => {
            submitRepay(item.billId)
          }}
        emptyUnpaid={(
          !Array.isArray(overduePayments) || overduePayments.length === 0)
          && (!Array.isArray(upcomingPayments) || upcomingPayments.length === 0
        )}
        paidPayments={paidPayments} />
    );
  }

  return (
    <MiniRepaymentHistoryContext.Provider value={{ tabKey: activeTab, setTabKey: setActiveTab as (v: string) => void }}>
      <Page>
        <Tabs
          tabs={[
          { value: 'unpaid', label: 'Phải trả', badge: history?.overduePayments?.length?.toString() },
          { value: 'paid', label: 'Đã trả' },
        ]}
        activeTab={activeTab}
        onChange={handleTabChange}
      />
      <Content>
          {renderContent()}
        </Content>
      </Page>
    </MiniRepaymentHistoryContext.Provider>
  );
}



type TabKey = 'paid' | 'unpaid';

const Page = styled.div`
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const Content = styled.div`
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  position: relative;
  &::-webkit-scrollbar {
    display: none;
  }
`;
