import { <PERSON><PERSON><PERSON> } from "bnpl-shared/src/constants";
import { MiniBNPLOnboardingNextStep } from "bnpl-shared/src/types";

export function getMiniBNPLScreenKeyFromOnboardingNextStep(onboarding_next_step?: string | MiniBNPLOnboardingNextStep): string {
  if (!onboarding_next_step) {
    return ScreenKey.MPOnboardingScreen
  }
  switch (onboarding_next_step) {
    case MiniBNPLOnboardingNextStep.ONBOARDING:
    case MiniBNPLOnboardingNextStep.VERIFY_FACE:
    case MiniBNPLOnboardingNextStep.QUERY_STATUS:
    case MiniBNPLOnboardingNextStep.REJECT:
      return ScreenKey.MPOnboardingScreen
    case MiniBNPLOnboardingNextStep.HOME:
    case MiniBNPLOnboardingNextStep.FINISH:
      return ScreenKey.MPHomeScreen
    default:
      return ScreenKey.MPHomeScreen
  }
}

export default getMiniBNPLScreenKeyFromOnboardingNextStep;
