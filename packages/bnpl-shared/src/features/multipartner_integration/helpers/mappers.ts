import { PartnerData, RoutingInfo } from 'bnpl-shared/src/types';

export class RoutingInfoMapper {
  routingInfo: RoutingInfo;
  constructor(routingInfo: RoutingInfo) {
    this.routingInfo = routingInfo;
  }
  toPartnerData(): PartnerData {
    return {
      partner_code: this.routingInfo.partner_code,
      partner_name: this.routingInfo.partner_name,
      status: this.routingInfo.status,
      request_id: this.routingInfo.request_id,
      account_id: this.routingInfo.account_id,
      fee: this.routingInfo.fee,
      onboarding_next_step: this.routingInfo.onboarding_next_step,
      allows_installment_registration: this.routingInfo.allows_installment_registration
    };
  }
}
