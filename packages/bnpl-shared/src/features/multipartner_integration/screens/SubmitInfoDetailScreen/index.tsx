import { FullScreenFormLayout } from 'bnpl-shared/src/components/layouts/FullScreenFormLayout';
import React, { FC, Fragment, useCallback, useEffect, useRef, useState } from 'react';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { Platform, TouchableOpacity, View } from 'react-native';
import { AppColors, ScreenKey } from 'bnpl-shared/src/constants';
import {
  RequireAdditionalInfo,
  RequireAdditionalInfoRef,
} from 'bnpl-shared/src/screens/onboarding-flow/AddInformationScreen/components';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { Colors, StyleUtils, windowHeight } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { ChevronIcon } from 'bnpl-shared/src/shared/ChevronIcon';
import { hideLoading, showDialog, showLoading } from 'bnpl-shared/src/shared/ZaloPayModules';
import { DialogType } from 'bnpl-shared/src/shared/constants';
import { DefaultBindingData, useSubmitInfoDetailViewModel } from './useSubmitInfoDetailViewModel';
import withViewModel from 'bnpl-shared/src/hooks/withViewModel';
import { getBottomSafe } from 'bnpl-shared/src/utils';
import { ABTestingGroup, ExperimentName, ResourceState, RewardBannerKey } from 'bnpl-shared/src/types';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import {
  trackBod2TapCta,
  trackBod2ViewContract,
  trackBod2ViewKycDetail,
  trackBod2Visible,
  trackBod2RewardBannerLoad,
  trackBod2RewardBannerClickBanner,
  trackBod2RewardBannerClickShowAd,
} from 'bnpl-shared/src/screens/onboarding-flow/AddInformationScreen/tracking';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import { WebView } from 'react-native-webview';
import { AppModal } from 'bnpl-shared/src/local/AppModal';
import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import { images } from 'bnpl-shared/src/res';
import RewardBanner, { styles as rewardBannerStyle } from 'bnpl-shared/src/features/reward_banner';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { isFullScreen } from 'bnpl-shared/src/components/NavigationWrapper';
import { FeeFreeTrialBanner } from '../OnboardingScreen/components/FeeFreeTrial';

const ProgressBar = ({ stepBar, stepPoint }: { stepBar: number; stepPoint: number }) => {
  return (
    <View style={[styles.progressBarRoot]}>
      <View style={styles.progressBar}>
        <View style={[styles.stepProgress, stepBar >= 1 && styles.stepActive]} />
        <View style={[styles.stepProgress, stepBar >= 2 && styles.stepActive]} />
        <View style={[styles.stepProgress, stepBar >= 3 && styles.stepActive]} />
        <View style={[styles.stepProgress, stepBar === 4 && styles.stepActive]} />
      </View>

      <View style={[styles.circlePoint, stepPoint >= 1 && styles.stepActive]}>
        <AppImage source={images.IconStepProgress1} height={10} width={10} />
      </View>
      <View style={[styles.circlePoint, stepPoint >= 2 && styles.stepActive]}>
        <AppImage
          source={images.IconStepProgress2}
          style={{ marginRight: 1, marginBottom: 1 }}
          height={10}
          width={10}
        />
      </View>
      <View style={[styles.circlePoint, stepPoint >= 3 && styles.stepActive]}>
        <AppImage source={images.IconStepProgress3} height={10} width={10} />
      </View>
    </View>
  );
};

const SubmitInfoDetailScreenView: FC<ReturnType<typeof useSubmitInfoDetailViewModel>> = props => {
  const { actions } = props;
  const { old_id_number } = useAppSelector(state => state.lotteOnboardingData);
  const abTesting = useAppSelector(state => state.abTesting);
  const requireInfoInputRef = useRef<RequireAdditionalInfoRef>(null);
  const [contractVisible, setContractVisible] = useState(false);
  useEffect(() => {
    if (
      props.defaultBindingData?.state === ResourceState.INIT ||
      props.defaultBindingData?.state === ResourceState.LOADING
    ) {
      showLoading();
      setSubmitting(true);
    } else {
      hideLoading();
      setSubmitting(false);
    }
  }, [props.defaultBindingData]);

  useEffect(() => {
    trackBod2Visible();
  }, []);

  const handleViewDetailInfo = useCallback(
    (uiData: DefaultBindingData) => {
      const bindingData = [
        { title: 'Họ tên', value: uiData.full_name || '' },
        { title: 'CCCD', value: uiData.id_number || '' },
        { title: 'Số điện thoại', value: uiData.phone_number || '' },
        { title: 'Giới tính', value: uiData.gender === 'MALE' ? 'Nam' : 'Nữ' },
        { title: 'Ngày sinh', value: uiData.birthday || '' },
        { title: 'Ngày cấp', value: uiData.id_issued_date || '' },
        { title: 'Ngày hết hạn', value: uiData.id_expired_date || '' },
        { title: 'Nơi cấp', value: uiData.id_issued_location || '' },
        { title: 'Quê quán', value: uiData.living_city || '' },
        { title: 'Địa chỉ thường trú', value: uiData.temp_residence_address || '' },
        { title: 'Quốc tịch', value: uiData.nationality || '' },
        { title: 'Chân dung', value: uiData.avatar },
      ];
      props.navigation.navigate(ScreenKey.PersonalExtraDataScreen, {
        bindingData,
        showDetailInfo: false,
        partnerCode: props.partnerCode,
      });
    },
    [props.navigation, props.defaultBindingData],
  );

  const [submitting, setSubmitting] = useState(false);
  const submittingRef = useRef(submitting);
  const handleSubmitData = async () => {
    try {
      if (submittingRef.current) {
        return;
      }

      if (!requireInfoInputRef?.current?.validateInput()) {
        return;
      }
      setSubmitting(true);
      submittingRef.current = true;
      showLoading();
      await actions.submitDetailProfile({
        occupation_code: actions.getValueForResourceType('OCCUPATION'),
        job_title_code: actions.getValueForResourceType('JOB_TITLE'),
        monthly_income_code: actions.getValueForResourceType('INCOME'),
        statement_date: actions.getValueForResourceType('STATEMENT_DATE'),
        old_id_number: old_id_number || '',
      });
    } catch (e: any) {
      showDialog?.(DialogType.INFO, 'Úi có gì đó không đúng', 'Mong bạn thông cảm và thử lại sau', ['Đóng']);
    } finally {
      hideLoading();
      setSubmitting(false);
      submittingRef.current = false;
    }
  };

  const fullName = props.defaultBindingData?.data?.full_name || 'Bạn';
  const isInFreeTrialWhiteList = abTesting[ExperimentName.LOTTE_FREE_TRIAL]?.toLowerCase() === ABTestingGroup.Variation_1.toLowerCase();
  return (
    <>
      <FullScreenFormLayout
        offsetDisabled={true}
        style={styles.root}
        contentStyles={{ paddingHorizontal: 0 }}
        actionsStyles={{ paddingHorizontal: 0 }}
        content={
          <Fragment>
            <Spacer height={12} />
            <ProgressBar stepBar={1} stepPoint={1} />
            <Spacer height={12} />
            <PersonalInfoButton
              onPress={() => {
                trackBod2ViewKycDetail();
                if (props.defaultBindingData.data) {
                  handleViewDetailInfo(props.defaultBindingData.data);
                }
              }}
              left="Thông tin cá nhân"
              right={fullName}
            />

            <RequireAdditionalInfo
              requireResources={['OCCUPATION', 'JOB_TITLE', 'INCOME', 'STATEMENT_DATE']}
              ref={requireInfoInputRef}
            />
          </Fragment>
        }
        actions={
          <View style={styles.bottomWrapper}>
            <View style={styles.desc}>
              <AppText>
                Bằng cách nhấn “Gửi hồ sơ”, tôi xác nhận đã đọc, đồng ý với nội dung{' '}
                <LinkButton
                  onPress={() => {
                    trackBod2ViewContract();
                    setContractVisible(true);
                  }}>
                  Hợp đồng mẫu
                </LinkButton>{' '}
                của LOTTE Finance.
              </AppText>
            </View>
            {isInFreeTrialWhiteList ? (
              <View style={{ backgroundColor: AppColors.background2 }}>
              <FeeFreeTrialBanner expandable={false} initialExpanding={false} noIconAnimation={true} borderTopRadius={true} />
              </View>
            ) : (
              <RewardBanner
                partner={props.navigation?.getParam('partnerCode')?.toLocaleLowerCase()}
                onLoad={trackBod2RewardBannerLoad}
                onPressAdBanner={trackBod2RewardBannerClickBanner}
                onPressShowAd={trackBod2RewardBannerClickShowAd}
                bannerKey={RewardBannerKey.Bod2}
              />
            )}
            <View style={styles.ctaWrapper}>
              <AppButton
                testID="submit-bod2"
                disabled={submitting}
                title={'Gửi hồ sơ'}
                buttonStyle={styles.button}
                onPress={async () => {
                  trackBod2TapCta();
                  await handleSubmitData();
                }}
              />
            </View>
          </View>
        }
      />
      <AppModal transparent visible={contractVisible} onRequestClose={() => setContractVisible(false)}>
        <BottomSheetLayout
          title={'Xem hợp đồng'}
          onRequestClose={() => setContractVisible(false)}
          content={
            <View style={[styles.bodyStyle, isFullScreen() && styles.bodyFullScreen]}>
              <WebView
                source={{ uri: avoidCacheImageUrl('https://simg.zalopay.com.vn/fs/bnpl/document/lotte_contract.html') }}
              />
            </View>
          }
        />
      </AppModal>
    </>
  );
};

export default withViewModel(useSubmitInfoDetailViewModel, SubmitInfoDetailScreenView);

const styles = StyleSheet.create({
  root: {
    backgroundColor: AppColors.background2,
    flex: 1,
    height: 'calc(100vh - 38px)',
  },
  input: {
    marginBottom: 12,
    marginHorizontal: 16,
  },
  newAddress: {
    marginTop: 4,
    marginBottom: 12,
  },
  sex: {
    marginBottom: 22,
  },
  confirmCheckbox: {
    marginTop: 10,
    marginBottom: 14,
  },
  section: { backgroundColor: AppColors.background, padding: 16 },
  button: {
    width: '100%',
  },
  bottomWrapper: {
    width: '100%',
    paddingBottom: getBottomSafe() + 16,
    backgroundColor: AppColors.background,
  },
  bodyStyle: {
    height: windowHeight * 0.75,
  },
  bodyFullScreen: {
    height: windowHeight * 0.65,
  },
  progressBarRoot: {
    width: '100%',
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 18,
  },
  circlePoint: {
    backgroundColor: Colors.disabled,
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepActive: {
    backgroundColor: Colors.primary,
    marginRight: -1,
  },
  progressBar: {
    backgroundColor: Colors.primary2,
    height: 3,
    width: '70%',
    position: 'absolute',
    bottom: 9,
    left: '15%',
    flexDirection: 'row',
  },
  stepProgress: {
    backgroundColor: Colors.primary2,
    height: 3,
    flex: 1,
  },
  desc: {
    padding: 16,
    backgroundColor: AppColors.background2,
  },
  ctaWrapper: {
    paddingTop: 0,
    paddingHorizontal: 16,
  },
});

//#region
const PersonalInfoButton: FC<{ left: string; right: string; onPress: () => void }> = ({ left, right, onPress }) => {
  const styles = personalInfoButtonStyles;
  return (
    <TouchableOpacity onPress={onPress} style={[StyleUtils.rowStretchBetween, styles.root]}>
      <AppText>{left}</AppText>
      <View style={StyleUtils.flexRow}>
        <AppText>{right}</AppText>
        <Spacer width={8} />
        <ChevronIcon direction="right" />
      </View>
    </TouchableOpacity>
  );
};

const personalInfoButtonStyles = StyleSheet.create({
  root: {
    borderWidth: 1,
    borderColor: Colors.primary2,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 15,
    marginHorizontal: 16,
    backgroundColor: AppColors.background,
  },
});
//#endregion
