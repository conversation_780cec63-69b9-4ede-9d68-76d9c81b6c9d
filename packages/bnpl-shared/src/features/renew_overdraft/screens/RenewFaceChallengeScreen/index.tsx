import React, { FC } from 'react';
import { NavigationScreenProp } from 'react-navigation';
import { FaceChallengeSubScreen } from 'bnpl-shared/src/screens/onboarding-flow/AddInformationScreen/FaceChallengeSubScreen';
import { LinkType, openCommonLink } from 'bnpl-shared/src/utils/openCommonLink';
import { useNavigation } from 'bnpl-shared/src/shared/navigation';
import { useRenewOverDraft } from 'bnpl-shared/src/features/renew_overdraft';
import { setAppToast, ToastType } from 'bnpl-shared/src/redux/appToastReducer';
import { useDispatch } from 'react-redux';
import get from 'lodash/get';
import { hideLoading, showLoading } from 'bnpl-shared/src/shared/ZaloPayModules';

export const RenewFaceChallengeScreen: FC<{ navigation: NavigationScreenProp<{}> }> = props => {
  const navigation = useNavigation(props);
  const dispatch = useDispatch();
  const { verifyRenewFaceChallenge, getRenewInfo, fetchRenewStatus } = useRenewOverDraft();

  const showErrorAppToast = (message: string) => {
    dispatch(setAppToast({ message, type: ToastType.ERROR, duration: 3000 }));
  };

  const handleFaceChallengeCompleted = async (request_id: string) => {
    const appendixId = getRenewInfo()?.appendixId;
    if (appendixId && request_id) {
      try {
        showLoading();
        await verifyRenewFaceChallenge(appendixId, request_id);
        fetchRenewStatus();
        navigation?.goBack();
      } catch (e: any) {
        showErrorAppToast(get(e, 'message', 'Đã có lỗi xảy ra, vui lòng thử lại sau'));
      } finally {
        hideLoading();
      }
    }
  };

  const onTermPress = () => {
    openCommonLink(LinkType.BNPL_TERMS, navigation);
  };

  return (
    <FaceChallengeSubScreen
      flow={'renew'}
      active={true}
      onTermPress={onTermPress}
      onContinue={handleFaceChallengeCompleted}
    />
  );
};
