import { useEffect, useState } from 'react';

export default function useFullScreen(): FullScreenConfig | null {
  const [fullScreenData, setFullScreenData] = useState<FullScreenConfig | null>(null);

  useEffect(() => {
    if (fullScreenData === null) {
      requestMiniAppData().then(setFullScreenData);
    }
  }, [fullScreenData]);

  return fullScreenData;
}

const DEBUGGING_KEY = 'bnpl_navigation_debugging';
const DEBUGGING_NAVIGATION_BAR_HEIGHT = 104;
const DEBUGGING = Boolean(window.localStorage.getItem(DEBUGGING_KEY));
const DEFAULT_HEIGHT = 88;
const windowAny = window as any;

export interface FullScreenConfig {
  fullScreen: boolean;
  navigationBarHeight: number;
}

async function requestMiniAppData() {
  try {
    const result = await windowAny.zlpSdk?.Utils?.getMiniAppData?.();
    if (DEBUGGING) {
      return {
        fullScreen: true,
        navigationBarHeight: DEBUGGING_NAVIGATION_BAR_HEIGHT,
      };
    }
    if (result?.status !== 'success') {
      return {
        fullScreen: false,
        navigationBarHeight: 0,
      };
    }
    if (result?.data?.miniappConfigData?.displayMode === 'full_without_safe_area') {
      const { topContentPadding } = result?.data?.miniappConfigData || {};
      return {
        fullScreen: true,
        navigationBarHeight: topContentPadding || DEFAULT_HEIGHT,
      };
    }
    return {
      fullScreen: false,
      navigationBarHeight: 0,
    };
  } catch (error: any) {
    return {
      fullScreen: false,
      navigationBarHeight: 0,
    };
  }
}
