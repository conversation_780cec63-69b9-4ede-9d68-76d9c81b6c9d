import { avoidCacheImageUrl } from 'bnpl-shared/src/shared/utils';
import { TimeUnit } from 'bnpl-shared/src/types';

const CDN_BASE_URL = 'https://simg.zalopay.com.vn/fs/bnpl/image';

const mapCdnImageToImageUriObj = (image: string) => {
  const transformedUrl = avoidCacheImageUrl(`${CDN_BASE_URL}/${image}`, { cacheIn: TimeUnit.DAYS });
  return { uri: transformedUrl };
};

export const cdn_images: any = {
  BackgroundPattern: mapCdnImageToImageUriObj('BackgroundPattern.png'),
  BackgroundRepaymentWidgetActive: mapCdnImageToImageUriObj('BackgroundRepaymentWidgetActive.png'),
  BackgroundRepaymentWidgetAlert: mapCdnImageToImageUriObj('BackgroundRepaymentWidgetAlert.png'),
  BackgroundRepaymentWidgetWarning: mapCdnImageToImageUriObj('BackgroundRepaymentWidgetWarning.png'),
  BackgroundTopLeftGradient: mapCdnImageToImageUriObj('BackgroundTopLeftGradient.png'),
  BackgroundUnbind: mapCdnImageToImageUriObj('BackgroundUnbind.png'),
  BannerAutoRepayBinding: mapCdnImageToImageUriObj('BannerAutoRepayBinding.png'),
  BannerIntroduce1: mapCdnImageToImageUriObj('BannerIntroduce1.png'),
  BannerIntroduce2: mapCdnImageToImageUriObj('BannerIntroduce2.png'),
  BannerIntroduce3: mapCdnImageToImageUriObj('BannerIntroduce3.png'),
  BannerIntroduce4: mapCdnImageToImageUriObj('BannerIntroduce4.png'),
  BannerConfirmUpdateRenewInfo: mapCdnImageToImageUriObj('BannerConfirmUpdateRenewInfo.png'),
  ImageAfterOTPArtwork: mapCdnImageToImageUriObj('ImageAfterOTPArtwork.png'),
  ImageApprovedDialogArtwork: mapCdnImageToImageUriObj('ImageApprovedDialogArtwork.png'),
  ImageArtwork1: mapCdnImageToImageUriObj('ImageArtwork1.png'),
  ImageArtwork2: mapCdnImageToImageUriObj('ImageArtwork2.png'),
  ImageArtwork3: mapCdnImageToImageUriObj('ImageArtwork3.png'),
  ImageArtwork4: mapCdnImageToImageUriObj('ImageArtwork4.png'),
  ImageCheckBoard: mapCdnImageToImageUriObj('ImageCheckBoard.png'),
  ImageConfirmExit: mapCdnImageToImageUriObj('ImageConfirmExit.png'),
  ImageConfirmExit2: mapCdnImageToImageUriObj('ImageConfirmExit2.png'),
  ImageContractProcessing: mapCdnImageToImageUriObj('ImageContractProcessing.png'),
  ImageContractReject: mapCdnImageToImageUriObj('ImageContractReject.png'),
  ImageContractSuccess: mapCdnImageToImageUriObj('ImageContractSuccess.png'),
  ImageKycLiveness: mapCdnImageToImageUriObj('ImageKycLiveness.png'),
  ImageLockAccount: mapCdnImageToImageUriObj('ImageLockAccount.png'),
  ImageLockout: mapCdnImageToImageUriObj('ImageLockout.png'),
  ImageLockoutNonWhitelist: mapCdnImageToImageUriObj('ImageLockoutNonWhitelist.png'),
  ImageLockoutRejected: mapCdnImageToImageUriObj('ImageLockoutRejected.png'),
  ImageMaintenance: mapCdnImageToImageUriObj('ImageMaintenance.png'),
  ImageOnboardingBackground: mapCdnImageToImageUriObj('ImageOnboardingBackground.png'),
  ImageOverRepayment: mapCdnImageToImageUriObj('ImageOverRepayment.png'),
  ImageRibbonSuggest: mapCdnImageToImageUriObj('ImageRibbonSuggest.png'),
  ImageSignPadCover: mapCdnImageToImageUriObj('ImageSignPadCover.png'),
  ImageStatementCompleted: mapCdnImageToImageUriObj('ImageStatementCompleted.png'),
  ImageSystemError: mapCdnImageToImageUriObj('ImageSystemError.png'),
  ImageType3Artwork: mapCdnImageToImageUriObj('ImageType3Artwork.png'),
  ImageUnbindArtwork1: mapCdnImageToImageUriObj('ImageUnbindArtwork1.png'),
  ImageUnbindArtwork2: mapCdnImageToImageUriObj('ImageUnbindArtwork2.png'),
  ImageUnbindArtwork3: mapCdnImageToImageUriObj('ImageUnbindArtwork3.png'),
  ImageUnbindArtwork4: mapCdnImageToImageUriObj('ImageUnbindArtwork4.png'),
  AhaMoveService: mapCdnImageToImageUriObj('AhaMoveService.png'),
  BaeminService: mapCdnImageToImageUriObj('BaeminService.png'),
  BigCService: mapCdnImageToImageUriObj('BigCService.png'),
  CircleKService: mapCdnImageToImageUriObj('CircleKService.png'),
  ComboService: mapCdnImageToImageUriObj('ComboService.png'),
  CoopMartService: mapCdnImageToImageUriObj('CoopMartService.png'),
  DienService: mapCdnImageToImageUriObj('DienService.png'),
  EmptyTransaction: mapCdnImageToImageUriObj('EmptyTransaction.png'),
  HoaDonDienService: mapCdnImageToImageUriObj('HoaDonDienService.png'),
  HoaDonNuocService: mapCdnImageToImageUriObj('HoaDonNuocService.png'),
  HocPhiService: mapCdnImageToImageUriObj('HocPhiService.png'),
  InternetService: mapCdnImageToImageUriObj('InternetService.png'),
  KFCService: mapCdnImageToImageUriObj('KFCService.png'),
  KhachSanService: mapCdnImageToImageUriObj('KhachSanService.png'),
  MuaBaoHiemService: mapCdnImageToImageUriObj('MuaBaoHiemService.png'),
  Nap3G4GService: mapCdnImageToImageUriObj('Nap3G4GService.png'),
  NapTienDienThoaiService: mapCdnImageToImageUriObj('NapTienDienThoaiService.png'),
  NuocService: mapCdnImageToImageUriObj('NuocService.png'),
  PhiChungCuService: mapCdnImageToImageUriObj('PhiChungCuService.png'),
  PhimService: mapCdnImageToImageUriObj('PhimService.png'),
  QuetMaQRService: mapCdnImageToImageUriObj('QuetMaQRService.png'),
  SeeMoreServices: mapCdnImageToImageUriObj('SeeMoreServices.png'),
  SevenElevenService: mapCdnImageToImageUriObj('SevenElevenService.png'),
  TheCoffeeHouseService: mapCdnImageToImageUriObj('TheCoffeeHouseService.png'),
  TraSauService: mapCdnImageToImageUriObj('TraSauService.png'),
  TruyenHinhService: mapCdnImageToImageUriObj('TruyenHinhService.png'),
  UniPassService: mapCdnImageToImageUriObj('UniPassService.png'),
  VETCService: mapCdnImageToImageUriObj('VETCService.png'),
  VeXeMayBayService: mapCdnImageToImageUriObj('VeXeMayBayService.png'),
  VieONService: mapCdnImageToImageUriObj('VieONService.png'),
  Tutorial1_1: mapCdnImageToImageUriObj('/guide/Tutorial1_1.png'),
  Tutorial1_2: mapCdnImageToImageUriObj('/guide/Tutorial1_2.png'),
  Tutorial1_3: mapCdnImageToImageUriObj('/guide/Tutorial1_3.png'),
  Tutorial1_4: mapCdnImageToImageUriObj('/guide/Tutorial1_4.png'),
  Tutorial1_5: mapCdnImageToImageUriObj('/guide/Tutorial1_5.png'),
  Tutorial2_1: mapCdnImageToImageUriObj('/guide/Tutorial2_1.png'),
  Tutorial2_2: mapCdnImageToImageUriObj('/guide/Tutorial2_2.png'),
  Tutorial2_3: mapCdnImageToImageUriObj('/guide/Tutorial2_3.png'),
  Tutorial3_1: mapCdnImageToImageUriObj('/guide/Tutorial3_1.png'),
  Tutorial3_2: mapCdnImageToImageUriObj('/guide/Tutorial3_2.png'),
  Tutorial3_3: mapCdnImageToImageUriObj('/guide/Tutorial3_3.png'),
  Tutorial3_4: mapCdnImageToImageUriObj('/guide/Tutorial3_4.png'),
  Tutorial4_1: mapCdnImageToImageUriObj('/guide/Tutorial4_1.png'),
  Tutorial4_2: mapCdnImageToImageUriObj('/guide/Tutorial4_2.png'),
  Tutorial4_3: mapCdnImageToImageUriObj('/guide/Tutorial4_3.png'),
  Tutorial4_4: mapCdnImageToImageUriObj('/guide/Tutorial4_4.png'),
  ImageRiskContactSupport: mapCdnImageToImageUriObj('ImageRiskContactSupport.png'),
  ImageRiskFaceChallenge: mapCdnImageToImageUriObj('ImageRiskFaceChallenge.png'),
  ImageRiskUpdateKyc: mapCdnImageToImageUriObj('ImageRiskUpdateKyc.png'),
  ImageChangePhoneNumber: mapCdnImageToImageUriObj('ImageChangePhoneNumber.png'),
  ImageLockoutNonWhitelist_20112023: mapCdnImageToImageUriObj('ImageLockoutNonWhitelist_20112023.png'),
  ImageTerminateAccount: mapCdnImageToImageUriObj('ImageTerminateAccount.png'),
  ImageUserLockAccount: mapCdnImageToImageUriObj('ImageUserLockAccount.png'),
  ImageUserUnlockAccount: mapCdnImageToImageUriObj('ImageUserUnlockAccount.png'),
  ImageRepayTip: mapCdnImageToImageUriObj('ImageRepayTip.png'),
  ImageTransactionProcessing: mapCdnImageToImageUriObj('ImageTransactionProcessing.png'),
  ImageFaceChallenge: mapCdnImageToImageUriObj('ImageFaceChallenge.png'),
  ImageIssueNfc: mapCdnImageToImageUriObj('ImageIssueNfc.png'),
  BackgroundLandingReward: mapCdnImageToImageUriObj('BackgroundLandingReward.png'),
  ImageNfcInvalid: mapCdnImageToImageUriObj('ImageNfcInvalid.png'),
  ImageCreditScore: mapCdnImageToImageUriObj('ImageCreditScore.png'),
  ImageEmptyRepayment: mapCdnImageToImageUriObj('ImageEmptyRepayment.png'),
  ImageEmbeddedOnboardingCard: mapCdnImageToImageUriObj('/embedded-onboarding/Card.png'),
  ImageEmbeddedOnboardingCard1: mapCdnImageToImageUriObj('/embedded-onboarding/Card-1.png'),
  ImageEmbeddedOnboardingCard2: mapCdnImageToImageUriObj('/embedded-onboarding/Card-2.png'),
  ImageEmbeddedOnboardingCard3: mapCdnImageToImageUriObj('/embedded-onboarding/Card-3.png'),
  ImageEmbeddedOnboardingStatusRejected: mapCdnImageToImageUriObj('/embedded-onboarding/status-rejected.png'),
  ImageEmbeddedOnboardingStatusTimeout: mapCdnImageToImageUriObj('/embedded-onboarding/status-timeout.png'),
  ImageUpdateNFC: mapCdnImageToImageUriObj('ImageUpdateNFC.svg'),
  BackgroundUpdateNFCBanner: mapCdnImageToImageUriObj('BackgroundUpdateNFCBanner.svg'),
  IconArrowNextOutlineActive: mapCdnImageToImageUriObj('IconArrowNextOutlineActive.svg'),
  ImageEmptyMiniRepaymentList1: mapCdnImageToImageUriObj('mini-repayment-list/empty_repayment_list_1.png'),
  ImageEmptyMiniRepaymentList2: mapCdnImageToImageUriObj('mini-repayment-list/empty_repayment_list_2.png'),
  ImageEmptyMiniRepaymentList3: mapCdnImageToImageUriObj('mini-repayment-list/empty_repayment_list_3.png'),
  ImageMiniEmptyReminder: mapCdnImageToImageUriObj('mini-repayment-list/empty_reminder.svg'),
  IconSupportCenter: mapCdnImageToImageUriObj('mini-repayment-list/ic_support_center.svg'),
  LogoMiniBNPL: mapCdnImageToImageUriObj('LogoMiniBnpl.svg'),
  IconMiniBNPLBuyTutorial: mapCdnImageToImageUriObj('icon_mini_bnpl_buy_tutorial.png'),
  ServiceTelcoTopup: mapCdnImageToImageUriObj('services/service_telco_topup.png'),
  ServiceEntertainmentMovie: mapCdnImageToImageUriObj('services/service_entertain_movie.png'),
  ServiceComingSoon: mapCdnImageToImageUriObj('services/service_coming_soon.png'),
  BackgroundMiniBNPLOnboardingStepRegister: mapCdnImageToImageUriObj('mini-bnpl/bg_onboarding_register.svg'),
  BackgroundMiniBNPLOnboardingStepApproving: mapCdnImageToImageUriObj('mini-bnpl/bg_onboarding_approving.svg'),
  LogoMiniBNPLGradient: mapCdnImageToImageUriObj('mini-bnpl/logo_mini_bnpl_gradient.png'),
  MiniBNPLFaceChallengeAvatar: mapCdnImageToImageUriObj('mini-bnpl/face_challenge_avatar.svg'),
  MiniBNPLBenefitPay37Days: mapCdnImageToImageUriObj('mini-bnpl/benefit_pay_37_days.svg'),
  MiniBNPLBenefitFeeFree: mapCdnImageToImageUriObj('mini-bnpl/benefit_fee_free.svg'),
  MiniBNPLBenefitEasy3Minutes: mapCdnImageToImageUriObj('mini-bnpl/benefit_easy_3_minutes.svg'),
  MiniBNPLIntroNoFeeNoInterest: mapCdnImageToImageUriObj('mini-bnpl/intro_no_fee_no_interest.png'),
  MiniBNPLIntroBuyNowPayLater: mapCdnImageToImageUriObj('mini-bnpl/intro_buy_now_pay_later.png'),
  MiniBNPLIntroMultipleServices: mapCdnImageToImageUriObj('mini-bnpl/intro_multiple_services.png'),
  MiniBNPLIntroNoFeeNoInterestContent: mapCdnImageToImageUriObj('mini-bnpl/intro_no_fee_no_interest_content.png'),
  MiniBNPLIntroBuyNowPayLaterContent: mapCdnImageToImageUriObj('mini-bnpl/intro_buy_now_pay_later_content.png'),
  MiniBNPLIntroMultipleServicesContent: mapCdnImageToImageUriObj('mini-bnpl/intro_multiple_services_content.png'),
  MiniBNPLPayTutorialStep1: mapCdnImageToImageUriObj('mini-bnpl/pay_tutorial_step_1.png'),
  MiniBNPLPayTutorialStep2: mapCdnImageToImageUriObj('mini-bnpl/pay_tutorial_step_2.png'),
  MiniBNPLPayTutorialStep3: mapCdnImageToImageUriObj('mini-bnpl/pay_tutorial_step_3.png'),
  MiniBNPLPayTutorialStep4: mapCdnImageToImageUriObj('mini-bnpl/pay_tutorial_step_4.png'),
  MiniBNPLPayTutorialStep5: mapCdnImageToImageUriObj('mini-bnpl/pay_tutorial_step_5.png'),
  MiniBNPLRepayTutorialStep1: mapCdnImageToImageUriObj('mini-bnpl/repay_tutorial_step_1.png'),
  MiniBNPLRepayTutorialStep2: mapCdnImageToImageUriObj('mini-bnpl/repay_tutorial_step_2.png'),
  MiniBNPLRepayTutorialStep3: mapCdnImageToImageUriObj('mini-bnpl/repay_tutorial_step_3.png'),
  MiniBNPLSliderBack: mapCdnImageToImageUriObj('mini-bnpl/icon_slider_back.svg'),
  MiniBNPLSliderNext: mapCdnImageToImageUriObj('mini-bnpl/icon_slider_next.svg'),
  MiniBNPLAccountRejected: mapCdnImageToImageUriObj('mini-bnpl/account_rejected.png'),
  MiniBNPLOnboardingStatusRejected: mapCdnImageToImageUriObj('mini-bnpl/status-rejected.png'),
  ImageFeeFreeTrial: mapCdnImageToImageUriObj('fee_free_trial.png'),
  IconFreeTrialFree: mapCdnImageToImageUriObj('icon_free_trial_free.svg'),
  IconFreeTrialNoInterest: mapCdnImageToImageUriObj('icon_free_trial_no_interest.svg'),
  IconFreeTrialNoti: mapCdnImageToImageUriObj('icon_free_trial_noti.svg'),
  ImageEmbeddedSignRenew: mapCdnImageToImageUriObj('embedded/sign_renew.png'),
  ImageIconIntroInstallment: mapCdnImageToImageUriObj('installment-intro/icon_intro_installment.png'),
  IconServiceTransferMoney: mapCdnImageToImageUriObj('installment-intro/service_transfer_money.svg'),
  IconGeneralTime: mapCdnImageToImageUriObj('installment-intro/general_time.svg'),
  IconGeneralSecurityCheck: mapCdnImageToImageUriObj('installment-intro/general_security_check.svg'),
  IconGeneralMoreCircle: mapCdnImageToImageUriObj('installment-intro/general_more_circle.svg'),
};
