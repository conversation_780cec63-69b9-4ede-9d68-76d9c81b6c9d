import { StatementDetailScreen } from 'bnpl-shared/src/screens';
import { store } from 'bnpl-shared/src/redux/store';
import { setStatementSummary } from 'bnpl-shared/src/redux/userStatementReducer';
import { ResourceState } from 'bnpl-shared/src/types';
import { getSummaryStatementApiBuilder } from 'bnpl-shared/src/api/__mocks__/getSummaryStatementApi';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import { FAKE_NAVIGATION } from 'bnpl-shared/src/jest/constants';
import React from 'react';
import { getHistoryPaymentApiBuilder } from 'bnpl-shared/src/api/__mocks__/getHistoryPaymentApi';
import { getHistoryPaymentApi } from 'bnpl-shared/src/api/getHistoryStatementApi';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { PartnerCode } from 'bnpl-shared/src/constants';

jest.mock('bnpl-shared/src/api/getHistoryStatementApi', () => ({
  getHistoryPaymentApi: jest.fn(),
}));

describe(StatementDetailScreen.name, () => {
  beforeAll(() => {
    (FAKE_NAVIGATION.getParam as jest.Mock).mockImplementation(args => {
      if (args === 'partnerCode') {
        return PartnerCode.CIMB;
      }
    });
    store.dispatch(setStatementSummary({ state: ResourceState.READY, data: getSummaryStatementApiBuilder.build() }));
  });
  it('render as expected', async () => {
    (getHistoryPaymentApi as jest.Mock).mockReturnValue(getHistoryPaymentApiBuilder.build());
    const { queryByText, queryByTestId, getByText } = renderWithRedux(
      <StatementDetailScreen navigation={FAKE_NAVIGATION} />,
    );
    expect(queryByText('Tóm tắt sao kê')).toBeTruthy();
    expect(queryByText('Thông tin thêm')).toBeTruthy();
    expect(queryByText('Đã thanh toán')).toBeTruthy();
    await waitFor(() => expect(queryByTestId('list-history-statement')).toBeTruthy());
    fireEvent.press(getByText('Đóng'));
    expect(FAKE_NAVIGATION.goBack).toHaveBeenCalled();
  });
});
