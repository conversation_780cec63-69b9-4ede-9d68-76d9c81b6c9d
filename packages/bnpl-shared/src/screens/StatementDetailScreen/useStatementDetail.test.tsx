import { useStatementDetail } from 'bnpl-shared/src/screens/StatementDetailScreen/useStatementDetail';
import { store } from 'bnpl-shared/src/redux/store';
import { setStatementSummary } from 'bnpl-shared/src/redux/userStatementReducer';
import { ResourceState } from 'bnpl-shared/src/types';
import { getSummaryStatementApiBuilder } from 'bnpl-shared/src/api/__mocks__/getSummaryStatementApi';
import { renderHookWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import React, { FC, ReactElement } from 'react';
import { render } from '@testing-library/react-native';
import { PartnerCode } from 'bnpl-shared/src/constants';

const Subject: FC<{ renderer: () => ReactElement | null }> = ({ renderer }) => {
  return renderer();
};

describe(useStatementDetail.name, () => {
  beforeAll(() => {
    store.dispatch(setStatementSummary({ state: ResourceState.READY, data: getSummaryStatementApiBuilder.build() }));
  });

  it('verify "Tóm tắt sao kê" section', () => {
    const sections = renderHookWithRedux(() => useStatementDetail(PartnerCode.CIMB)).result.current;
    expect(sections[0].title).toEqual('Tóm tắt sao kê');
    expect(sections[0].data).toEqual([
      { key: 'Dư nợ đến hạn', value: '5.000.000đ', bold: true },
      { key: 'Thanh toán tối thiểu', value: '5.000.000đ', bold: true },
      { key: 'Đến hạn thanh toán', value: '20:00 - 06/09/2022', bold: true },
    ]);
  });

  it('verify "Thông tin thêm" section', () => {
    const sections = renderHookWithRedux(() => useStatementDetail(PartnerCode.CIMB)).result.current;
    expect(sections[2].title).toEqual('Thông tin thêm');
    expect(sections[2].data).toEqual([
      { key: 'Hạn mức khả dụng', value: '5.000.000đ', bold: false },
      { key: 'Hạn mức tối đa', value: '5.000.000đ', bold: false },
      { key: 'Thời hạn khả dụng của hạn mức', value: '23/12/2008', bold: false },
      { key: 'Lãi suất cho các dư nợ phát sinh lãi (nếu có)', value: '0.1%/ngày', bold: false },
      { key: 'Dư nợ đầu kỳ', value: '5.000.000đ', bold: false },
      { key: 'Giao dịch mới trong kỳ', value: '5.000.000đ', bold: false },
      { key: 'Ngày sao kê', value: '23/12/2008', bold: false },
    ]);
  });

  it('verify "Đã thanh toán" section', () => {
    store.dispatch(
      setStatementSummary({
        state: ResourceState.READY,
        data: getSummaryStatementApiBuilder.build('statement_already_paid'),
      }),
    );

    const sections = renderHookWithRedux(() => useStatementDetail(PartnerCode.CIMB)).result.current;
    const { queryByText } = render(<Subject renderer={sections[1].renderer} />);
    expect(queryByText('Dư nợ đến hạn')).toBeTruthy();
  });
});
