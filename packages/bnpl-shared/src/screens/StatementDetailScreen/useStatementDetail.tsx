import { useCIMBStatementController } from 'bnpl-shared/src/utils/statement_controller/useCIMBStatementController';
import { formatDate } from 'bnpl-shared/src/shared/utils/formatDateStatement';
import { formatCurrency } from 'bnpl-shared/src/shared/utils/formatCurrency';
import React from 'react';
import { TwoSideView } from 'bnpl-shared/src/shared/TwoSideView/TwoSideView';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { View } from 'react-native';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { images } from 'bnpl-shared/src/res';
import { AppColors, PartnerCode } from 'bnpl-shared/src/constants';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { useLotteStatementController } from 'bnpl-shared/src/utils/statement_controller/useLotteStatementController';

export type DataMap = { key: string; value: string | undefined; bold: boolean };
export type Section = {
  title?: string;
  data?: DataMap[];
  renderer?: () => React.ReactElement | null;
};

const useCIMBStatementDetail = () => {
  const { userStatement, stmConditions } = useCIMBStatementController();

  if (!userStatement) {
    return [];
  }

  const renderRepaidField = (): React.ReactElement | null => {
    let value = formatCurrency(userStatement.repaid_amount);
    if (stmConditions.noSpendInPeriod) {
      return null;
    }
    if (stmConditions.isRepaid) {
      value = 'Dư nợ đến hạn';
    }
    return (
      <TwoSideView
        style={styles.repaidContainer}
        left={
          <View style={StyleUtils.flexRow}>
            {stmConditions.isRepaid && (
              <AppImage
                style={styles.tickIcon}
                width={20}
                height={20}
                source={images.IconCheckCircle}
                tintColor={AppColors.green[4]}
              />
            )}
            <AppText>Đã thanh toán</AppText>
          </View>
        }
        right={<AppText bold>{value}</AppText>}
      />
    );
  };

  return [
    {
      title: 'Tóm tắt sao kê',
      data: [
        { key: 'Dư nợ đến hạn', value: formatCurrency(userStatement.total_outstanding_amount), bold: true },
        { key: 'Thanh toán tối thiểu', value: formatCurrency(userStatement.min_pay_amount), bold: true },
        {
          key: 'Đến hạn thanh toán',
          value: `20:00 - ${formatDate(userStatement.repayment_grace_end_date)}`,
          bold: true,
        },
      ],
    },
    {
      renderer: renderRepaidField,
    },
    {
      title: 'Thông tin thêm',
      data: [
        { key: 'Hạn mức khả dụng', value: formatCurrency(userStatement.available_limit), bold: false },
        { key: 'Hạn mức tối đa', value: formatCurrency(userStatement.approved_limit), bold: false },
        { key: 'Thời hạn khả dụng của hạn mức', value: formatDate(userStatement.limit_expire_date), bold: false },
        { key: 'Lãi suất cho các dư nợ phát sinh lãi (nếu có)', value: '0.1%/ngày', bold: false },
        {
          key: 'Dư nợ đầu kỳ',
          value: formatCurrency(userStatement.total_outstanding_debt_obligation_amount),
          bold: false,
        },
        {
          key: 'Giao dịch mới trong kỳ',
          value: formatCurrency(userStatement.total_new_od_transaction_amount),
          bold: false,
        },
        {
          key: 'Ngày sao kê',
          value: formatDate(userStatement.statement_date),
          bold: false,
        },
      ],
    },
  ];
};

const useLotteStatementDetail = () => {
  const { userStatement } = useLotteStatementController();

  if (!userStatement) {
    return [];
  }

  return [
    {
      title: 'Tóm tắt sao kê',
      data: [
        {
          key: 'Dư nợ đến hạn',
          value: formatCurrency(Number(userStatement.statement_total_outstanding_balance)),
          bold: true,
        },
        {
          key: 'Đến hạn thanh toán',
          value: userStatement.statement_due_date_with_hour,
          bold: true,
        },
      ],
    },
    {
      title: 'Thông tin thêm',
      data: [
        {
          key: 'Ngày sao kê',
          value: formatDate(userStatement.statement_billing_date),
          bold: false,
        },
      ],
    },
  ];
};

export const useStatementDetail = (partnerCode: PartnerCode): Section[] => {
  const sectionCIMB = useCIMBStatementDetail();
  const sectionLotte = useLotteStatementDetail();
  return partnerCode === PartnerCode.CIMB ? sectionCIMB : sectionLotte;
};

const styles = StyleSheet.create({
  repaidContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: AppColors.background,
    marginTop: 12,
    marginHorizontal: 16,
  },
  tickIcon: {
    marginEnd: 16,
  },
});
