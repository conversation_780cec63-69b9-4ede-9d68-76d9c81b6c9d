import React from 'react';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { renderWithRedux } from 'bnpl-shared/src/jest/renderWithRedux';
import { store } from 'bnpl-shared/src/redux/store';
import { setBindingInfo } from 'bnpl-shared/src/redux/bindingReducer';
import { addDays, subYears } from 'date-fns';
import { formatDate } from 'bnpl-shared/src/shared/utils/date';
import { postDetailInfoApi } from 'bnpl-shared/src/api/postDetailInfoApi';
import { postDetailInfoApiBuilder } from 'bnpl-shared/src/api/__mocks__/postDetailInfoApi';
import {
  setChoicesForResourceType,
  setValueForResourceType,
} from 'bnpl-shared/src/screens/onboarding-flow/PersonalExtraDataScreen';
import * as spyHook from 'bnpl-shared/src/hooks/useHandleRejectionWhileOnboarding';
import { ResourceType } from 'bnpl-shared/src/types';
import { BOD2 } from 'bnpl-shared/src/screens/onboarding-flow/AddInformationScreen/BOD2';
import InfoModal from 'bnpl-shared/src/components/InfoModal';
import { infoModalRef } from 'bnpl-shared/src/services';

const mockPromptAuthChallengeFlow = jest.fn();

jest.mock('bnpl-shared/src/shared/ZaloPayModules', () => ({
  ...jest.requireActual('bnpl-shared/src/shared/ZaloPayModules'),
  usePromptAuthChallengeFlow: () => ({
    promptAuthChallengeFlow: mockPromptAuthChallengeFlow,
  }),
}));

const mockHandleOnboardingRejection = jest.fn();

jest.spyOn(spyHook, 'useHandleRejectionWhileOnboarding').mockReturnValue({
  handleOnboardingRejection: mockHandleOnboardingRejection,
});

jest.mock('bnpl-shared/src/hooks/useABTesting', () => ({
  useABTesting: () => ({
    fetchABTestResult: jest.fn(),
    isInWhiteList: jest.fn(),
    isInWhiteListRealtime: jest.fn(),
    getABTestResultByKey: jest.fn(),
  }),
}));

const dispatchChoiceAndValueMock = (...args: ResourceType[]) => {
  args.forEach(resourceType => {
    store.dispatch(
      setChoicesForResourceType({
        resourceType: resourceType,
        choices: [
          { id: '1', value: 'value_1' },
          { id: '2', value: 'value_2' },
          { id: '3', value: 'value_3' },
        ],
      }),
    );
    store.dispatch(setValueForResourceType({ resourceType, value: '1' }));
  });
};

beforeEach(() => {
  (postDetailInfoApi as jest.Mock).mockClear();
  store.dispatch(
    setBindingInfo({
      basic_profile: {
        full_name: 'Nguyen Van A',
        full_name_suggestion: 'Nguyen Van B',
      },
      detail_profile: {
        birthday: '',
        id_issued_date: '',
        id_issued_location: '',
        permanent_address: '',
        temp_residence_address: '',
        gender: '',
      },
    }),
  );
});

describe('BOD2', () => {
  it('requires fields', async () => {
    const { getByTestId, queryAllByText } = renderWithRedux(
      <BOD2 loadFrom="application_progress" active onContinue={jest.fn} onBack={jest.fn} />,
    );
    fireEvent.press(getByTestId('btn-input-temp-address'));
    fireEvent.press(getByTestId('submit-bod2'));
    await waitFor(() => expect(queryAllByText('Thông tin không được bỏ trống').length).toEqual(3));
  });

  it('shows button with user full name that leads to personal info view', async () => {
    const { queryByText } = renderWithRedux(
      <BOD2 loadFrom="application_progress" active onContinue={jest.fn} onBack={jest.fn} />,
    );
    await waitFor(() => expect(queryByText('Nguyen Van A')).toBeTruthy());
  });

  it('validate birthday', async () => {
    const { getByTestId, queryByText } = renderWithRedux(
      <BOD2 loadFrom="application_progress" active onContinue={jest.fn} onBack={jest.fn} />,
    );
    fireEvent.changeText(getByTestId('birthday'), formatDate(subYears(new Date(), 10)));
    fireEvent.press(getByTestId('submit-bod2'));
    await waitFor(() => expect(queryByText('Bạn chưa đủ 18 tuổi')).toBeTruthy());

    fireEvent.changeText(getByTestId('birthday'), '13/13/2000');
    fireEvent.press(getByTestId('submit-bod2'));
    await waitFor(() => expect(queryByText('Ngày sinh không hợp lệ')).toBeTruthy());

    fireEvent.changeText(getByTestId('birthday'), formatDate(addDays(new Date(), 10)));
    fireEvent.press(getByTestId('submit-bod2'));
    await waitFor(() => expect(queryByText('Ngày sinh phải ở trong quá khứ')).toBeTruthy());
  });

  it('validate identityIssuedDate', async () => {
    const { getByTestId, queryByText } = renderWithRedux(
      <BOD2 loadFrom="application_progress" active onContinue={jest.fn} onBack={jest.fn} />,
    );

    fireEvent.changeText(getByTestId('identityIssuedDate'), '13/13/2000');
    fireEvent.press(getByTestId('submit-bod2'));
    await waitFor(() => expect(queryByText('Ngày cấp CMND/CCCD không hợp lệ')).toBeTruthy());

    fireEvent.changeText(getByTestId('identityIssuedDate'), formatDate(addDays(new Date(), 10)));
    fireEvent.press(getByTestId('submit-bod2'));
    await waitFor(() => expect(queryByText('Ngày cấp CMND/CCCD phải ở trong quá khứ')).toBeTruthy());
  });

  it('prevent submit if not yet confirm', () => {
    // TODO: jest.fn is wrong, jest.fn()
    const { getByTestId } = renderWithRedux(
      <BOD2 loadFrom="application_progress" active onContinue={jest.fn} onBack={jest.fn} />,
    );
    fireEvent.press(getByTestId('confirm-checkbox'));
    expect(getByTestId('submit-bod2').props.disabled).toBeFalsy();
  });

  it('shows the type 3 dialog', async () => {
    dispatchChoiceAndValueMock('OCCUPATION', 'CITY', 'JOB_TITLE', 'INCOME');
    (postDetailInfoApi as jest.Mock).mockResolvedValueOnce(postDetailInfoApiBuilder.build('type3'));
    const onContinue = jest.fn();
    const onBack = jest.fn();
    const { getByText, getByTestId, queryByText } = renderWithRedux(
      <BOD2 loadFrom="application_progress" active onContinue={onContinue} onBack={onBack} />,
    );

    fireEvent.press(getByText('Nam'));
    fireEvent.changeText(getByTestId('birthday'), '12/12/2000');
    fireEvent.changeText(getByTestId('identityIssuedDate'), '12/12/2010');
    fireEvent.press(getByText('Nơi cấp CMND/CCCD'));
    fireEvent.press(getByText('Bình Định'));
    fireEvent.changeText(getByTestId('address'), 'Somewhere over the rainbow');
    fireEvent.press(getByText('Tiếp tục'));
    await waitFor(() =>
      expect(
        queryByText(
          'Ngân hàng CIMB đã có thông tin của bạn. Bắt đầu liên kết với Zalopay để sử dụng Tài Khoản Trả Sau',
        ),
      ).toBeTruthy(),
    );
  });

  it('verify lending rule OLD_KYC is handled as expected', async () => {
    dispatchChoiceAndValueMock('OCCUPATION', 'CITY', 'JOB_TITLE', 'INCOME');
    (postDetailInfoApi as jest.Mock).mockResolvedValueOnce(postDetailInfoApiBuilder.build('lending_rule_old_kyc'));
    const onContinue = jest.fn();
    const onBack = jest.fn();
    const { getByText, getByTestId, queryByText } = renderWithRedux(
      <BOD2 loadFrom="application_progress" active onContinue={onContinue} onBack={onBack} />,
    );

    fireEvent.press(getByText('Nam'));
    fireEvent.changeText(getByTestId('birthday'), '12/12/2000');
    fireEvent.changeText(getByTestId('identityIssuedDate'), '12/12/2010');
    fireEvent.press(getByText('Nơi cấp CMND/CCCD'));
    fireEvent.press(getByText('Bình Định'));
    fireEvent.changeText(getByTestId('address'), 'Somewhere over the rainbow');
    fireEvent.press(getByText('Tiếp tục'));
    await waitFor(() => expect(queryByText('OLD_KYC title')).toBeTruthy());
    await waitFor(() => expect(queryByText('OLD_KYC description')).toBeTruthy());
  });

  it('verify edge case info is handled as expected', async () => {
    dispatchChoiceAndValueMock('OCCUPATION', 'CITY', 'JOB_TITLE', 'INCOME');
    (postDetailInfoApi as jest.Mock).mockResolvedValueOnce(postDetailInfoApiBuilder.build('edge_case_info'));
    const onContinue = jest.fn();
    const onBack = jest.fn();
    const { getByText, getByTestId, queryByTestId } = renderWithRedux(
      <BOD2 loadFrom="application_progress" active onContinue={onContinue} onBack={onBack} />,
    );

    fireEvent.press(getByText('Nam'));
    fireEvent.changeText(getByTestId('birthday'), '12/12/2000');
    fireEvent.changeText(getByTestId('identityIssuedDate'), '12/12/2010');
    fireEvent.press(getByText('Nơi cấp CMND/CCCD'));
    fireEvent.press(getByText('Bình Định'));
    fireEvent.changeText(getByTestId('address'), 'Somewhere over the rainbow');
    fireEvent.press(getByText('Tiếp tục'));
    await waitFor(() => expect(queryByTestId('edge-case-modal')).toBeTruthy());
  });

  it('handled account rejection while onboarding as expected', async () => {
    dispatchChoiceAndValueMock('OCCUPATION', 'CITY', 'JOB_TITLE', 'INCOME');
    (postDetailInfoApi as jest.Mock).mockRejectedValue(new Error('Account Rejected'));
    const onContinue = jest.fn();
    const onBack = jest.fn();
    const { getByText, getByTestId } = renderWithRedux(
      <BOD2 loadFrom="application_progress" active onContinue={onContinue} onBack={onBack} />,
    );

    fireEvent.press(getByText('Nam'));
    fireEvent.changeText(getByTestId('birthday'), '12/12/2000');
    fireEvent.changeText(getByTestId('identityIssuedDate'), '12/12/2010');
    fireEvent.press(getByText('Nơi cấp CMND/CCCD'));
    fireEvent.press(getByText('Bình Định'));
    fireEvent.changeText(getByTestId('address'), 'Somewhere over the rainbow');
    fireEvent.press(getByText('Tiếp tục'));
    await waitFor(() => expect(mockHandleOnboardingRejection).toHaveBeenCalled());
  });

  it('verify lending rule TONE_ACCENT_INVALID is handled as expected', async () => {
    const baseRespone = {
      birthday: '12/12/2000',
      education: '',
      employment_status: '',
      gender: 'MALE',
      id_issued_date: '12/12/2010',
      id_issued_location: 'Bình Định',
      job_title: '1',
      living_city: '1',
      loan_purpose: 'Thanh toán dịch vụ',
      monthly_income: '1',
      occupation: '1',
      permanent_address: 'Somewhere over the rainbow',
      request_id: '',
      source_of_fund: '',
      temp_residence_address: 'Somewhere over the rainbow',
      agreements: {
        installment_registration: false,
      },
    };
    dispatchChoiceAndValueMock('OCCUPATION', 'CITY', 'JOB_TITLE', 'INCOME');
    (postDetailInfoApi as jest.Mock).mockResolvedValueOnce(
      postDetailInfoApiBuilder.build('lending_tone_accent_invalid'),
    );
    const onContinue = jest.fn();
    const onBack = jest.fn();
    const { getByText, getByTestId, queryByText } = renderWithRedux(
      <BOD2 loadFrom="application_progress" active onContinue={onContinue} onBack={onBack} />,
    );

    fireEvent.press(getByText('Nam'));
    fireEvent.changeText(getByTestId('birthday'), '12/12/2000');
    fireEvent.changeText(getByTestId('identityIssuedDate'), '12/12/2010');
    fireEvent.press(getByText('Nơi cấp CMND/CCCD'));
    fireEvent.press(getByText('Bình Định'));
    fireEvent.changeText(getByTestId('address'), 'Somewhere over the rainbow');
    fireEvent.press(getByText('Tiếp tục'));
    await waitFor(() =>
      expect(postDetailInfoApi).toHaveBeenNthCalledWith(1, {
        ...baseRespone,
        full_name: 'Nguyen Van A',
      }),
    );
    await waitFor(() => expect(queryByText('TONE_ACCENT_INVALID title')).toBeTruthy());
    await waitFor(() => expect(queryByText('TONE_ACCENT_INVALID description')).toBeTruthy());
    fireEvent.press(getByText('Sử dụng "Nguyen Van B"'));
    expect(postDetailInfoApi).toHaveBeenNthCalledWith(2, {
      ...baseRespone,
      full_name: 'Nguyen Van B',
      request_id: '011231241231',
    });
  });

  it('verify lending rule LACK_OF_NFC_INFO is handled as expected', async () => {
    (mockPromptAuthChallengeFlow as jest.Mock).mockResolvedValue({ status: 1, resultData: 'test_data' });
    dispatchChoiceAndValueMock('OCCUPATION', 'CITY', 'JOB_TITLE', 'INCOME');
    (postDetailInfoApi as jest.Mock).mockResolvedValueOnce(postDetailInfoApiBuilder.build('lack_of_nfc_info'));
    const onContinue = jest.fn();
    const onBack = jest.fn();
    const { getByText, getByTestId, queryByText } = renderWithRedux(
      <BOD2 loadFrom="application_progress" active onContinue={onContinue} onBack={onBack} />,
    );

    fireEvent.press(getByText('Nam'));
    fireEvent.changeText(getByTestId('birthday'), '12/12/2000');
    fireEvent.changeText(getByTestId('identityIssuedDate'), '12/12/2010');
    fireEvent.press(getByText('Nơi cấp CMND/CCCD'));
    fireEvent.press(getByText('Bình Định'));
    fireEvent.changeText(getByTestId('address'), 'Somewhere over the rainbow');
    fireEvent.press(getByText('Tiếp tục'));
    await waitFor(() => expect(postDetailInfoApi).toHaveBeenCalledTimes(1));
    await waitFor(() => expect(queryByText('LACK_OF_NFC_INFO title')).toBeTruthy());
    await waitFor(() => expect(queryByText('LACK_OF_NFC_INFO description')).toBeTruthy());
    fireEvent.press(getByText('Thực hiện ngay'));
    expect(mockPromptAuthChallengeFlow).toHaveBeenCalledWith(3, 7);
    await waitFor(() => expect(postDetailInfoApi).toHaveBeenCalledTimes(2));
  });

  it('verify lending rule INVALID_NFC is handled as expected', async () => {
    dispatchChoiceAndValueMock('OCCUPATION', 'CITY', 'JOB_TITLE', 'INCOME');
    (postDetailInfoApi as jest.Mock).mockResolvedValueOnce(postDetailInfoApiBuilder.build('invalid_nfc'));
    const onContinue = jest.fn();
    const onBack = jest.fn();
    const { getByText, getByTestId, queryByText } = renderWithRedux(
      <>
        <BOD2 loadFrom="application_progress" active onContinue={onContinue} onBack={onBack} />
        <InfoModal ref={infoModalRef} />
      </>,
    );

    fireEvent.press(getByText('Nam'));
    fireEvent.changeText(getByTestId('birthday'), '12/12/2000');
    fireEvent.changeText(getByTestId('identityIssuedDate'), '12/12/2010');
    fireEvent.press(getByText('Nơi cấp CMND/CCCD'));
    fireEvent.press(getByText('Bình Định'));
    fireEvent.changeText(getByTestId('address'), 'Somewhere over the rainbow');
    fireEvent.press(getByText('Tiếp tục'));
    await waitFor(() => expect(postDetailInfoApi).toHaveBeenCalledTimes(1));
    await waitFor(() => expect(queryByText('Đồng ý')).toBeTruthy());
    await waitFor(() => expect(queryByText('INVALID_NFC title')).toBeTruthy());
    await waitFor(() => expect(queryByText('INVALID_NFC description')).toBeTruthy());
  });
});
