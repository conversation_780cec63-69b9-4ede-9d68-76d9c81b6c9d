//#region
import { postDetailInfoApi } from 'bnpl-shared/src/api/postDetailInfoApi';
import { FullScreenFormLayout } from 'bnpl-shared/src/components/layouts/FullScreenFormLayout';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppColors, PartnerCode, ScreenKey } from 'bnpl-shared/src/constants';
import { useAppSelector } from 'bnpl-shared/src/redux/store';
import { CheckBox } from 'bnpl-shared/src/shared/CheckBox/CheckBox';
import { ChevronIcon } from 'bnpl-shared/src/shared/ChevronIcon';
import { DateMaskInput } from 'bnpl-shared/src/shared/DateMaskInput';
import { InfoBox } from 'bnpl-shared/src/shared/InfoBox/InfoBox';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { NavigationContext } from 'bnpl-shared/src/shared/NavigationContext';
import { AppText, AppTextInput } from 'bnpl-shared/src/shared/react-native-customized';
import { SelectInput } from 'bnpl-shared/src/shared/SelectInput';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { Colors, StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { isValidDate, parseDate } from 'bnpl-shared/src/shared/utils/date';
import { invariant } from 'bnpl-shared/src/shared/utils/invariant';
import { useStorageState } from 'bnpl-shared/src/shared/utils/useStorageState';
import {
  hideLoading,
  launchKycUpdateDeeplink,
  launchLivenessDetectionDeeplink,
  showErrorDialog,
  showLoading,
  usePromptAuthChallengeFlow,
} from 'bnpl-shared/src/shared/ZaloPayModules';
import { differenceInYears, isAfter } from 'date-fns';
import React, { FC, Fragment, useContext, useEffect, useRef, useState } from 'react';
import { Controller, FieldError, useForm } from 'react-hook-form';
import { Platform, TouchableOpacity, View } from 'react-native';
import { usePersonalExtraDataController } from '../PersonalExtraDataScreen/usePersonalExtraDataController';
import ActionBottom from './ActionBottom';
import PlaceOfIssueFilterModal from './PlaceOfIssueFilterModal';
import { SelectSex } from './SelectSex';
import {
  trackBod2AddNewAddress,
  trackBod2InputGender,
  trackBod2ResultDialogClose,
  trackBod2ResultDialogVisible,
  trackBod2SubmitError,
  trackBod2TapAddress,
  trackBod2TapCheckbox,
  trackBod2TapCta,
  trackBod2TapIdentityIssueDate,
  trackBod2TapIdentityIssuePlace,
  trackBod2Visible,
  trackBod2RewardBannerLoad,
  trackBod2RewardBannerClickBanner,
  trackBod2RewardBannerClickShowAd,
} from './tracking';
import { Type3Modal } from './type3/Type3Modal';
import { Bod2_FormValues } from './types';
import UpdateAddressModal from './UpdateAddressModal';
import { useOnboardingId } from 'bnpl-shared/src/hooks/useOnboardingId';
import { RequireAdditionalInfo, RequireAdditionalInfoRef } from './components';
import { VerifyIssueModal } from 'bnpl-shared/src/screens/onboarding-flow/OnboardingScreen/bod1/VerifyIssueModal';
import {
  AuthChallengeType,
  EdgeCaseInfo,
  PredefinedCTA,
  RewardBannerKey,
  SignMethod,
  UMSource,
  UMStatus,
  VerifyIssue,
} from 'bnpl-shared/src/types';
import { useVerifyIssueMapper } from 'bnpl-shared/src/screens/onboarding-flow/OnboardingScreen/bod1/useSubmitBOD1Controller';
import { postCheckKycWhitelist } from 'bnpl-shared/src/api/postCheckKycWhitelist';
import { EdgeCaseModal } from 'bnpl-shared/src/screens/onboarding-flow/OnboardingScreen/bod1/EdgeCaseModal';
import { useHandleRejectionWhileOnboarding } from 'bnpl-shared/src/hooks/useHandleRejectionWhileOnboarding';
import { setAppToast, ToastType } from 'bnpl-shared/src/redux/appToastReducer';
import { useDispatch } from 'react-redux';
import { postInitOnboardingSignContractApi } from 'bnpl-shared/src/api/postInitOnboardingSignContractApi';
import RewardBanner, { styles as rewardBannerStyle } from 'bnpl-shared/src/features/reward_banner';
import { launchCreditScoreDeeplink } from 'bnpl-shared/src/utils/launchCreditScoreDeeplink';
import { postResetNfcApi } from 'bnpl-shared/src/api/postResetNfcApi';
import { InfoModalService, ModalType } from 'bnpl-shared/src/services';
import { EdgeCaseModalUI } from 'bnpl-shared/src/screens/onboarding-flow/OnboardingScreen/bod1/EdgeCaseModal/EdgeCaseModalUI';
import { useAuthChallengeSource } from 'bnpl-shared/src/hooks/useAuthChallengeSource';
import { closeMiniApp } from 'bnpl-shared/src/utils/closeMiniApp';
import { isAppVersionGreaterThanOrEqual } from 'bnpl-shared/src/utils/appVersion';
import { InstallmentRegistrationCheckbox } from './components/InstallmentRegistrationCheckbox';
import { usePartnerData } from 'bnpl-shared/src/features/multipartner_integration/helpers';

//#endregion

type Bod2_Props = {
  active: boolean;
  loadFrom: string;
  onContinue: () => void;
  onBack?: () => void;
  action?: string;
};

export const BOD2: FC<Bod2_Props> = ({ loadFrom, active, onBack, onContinue, action }) => {
  const dispatch = useDispatch();
  const hookOnboardingId = useOnboardingId();
  const { mapIssue } = useVerifyIssueMapper();
  const [onBoardingId, setOnBoardingId] = useState(hookOnboardingId);
  invariant(loadFrom, 'loadFrom must be provided!');
  const navigation = useContext(NavigationContext);
  const bindingInfo = useAppSelector(state => state.binding.binding_info);
  const appInfo = useAppSelector(state => state.appInfo);
  const { control, handleSubmit, setValue, getValues } = useForm<Bod2_FormValues>({
    mode: 'onBlur',
  });
  const { promptAuthChallengeFlow } = usePromptAuthChallengeFlow();
  const { handleOnboardingRejection } = useHandleRejectionWhileOnboarding();
  const [placeOfIssueModalVisible, setPlaceOfIssueModalVisible] = useState(false);
  const [placeOfIssue, setPlaceOfIssue] = useState('');
  const [updateAddressModalVisible, setUpdateAddressModalVisible] = useState(false);
  const [showTemporaryAddress, setShowTemporaryAddress] = useState(false);
  const [checkedConfirm, setCheckedConfirm] = useState(true);

  const [enteringAddress, setEnteringAddress] = useState(false);
  const [originValues, setOriginValues] = useState<Bod2_FormValues>();
  const [defaultValues, setDefaultValues] = useState<Bod2_FormValues>();
  const [storeUserData, setStoreUserData] = useStorageState('STORE_USER_DATA');
  const [type3Modal, setType3Modal] = useState(false);
  const authSource = useAuthChallengeSource();
  const requireInfoInputRef = useRef<RequireAdditionalInfoRef>(null);

  //lending rules for bod2
  const [foundIssue, setFoundIssue] = useState<VerifyIssue>();
  const [verifyModalVisible, setVerifyModalVisible] = useState(false);

  //handle reject code from bank when submit bod2
  const [edgeCaseInfo, setEdgeCaseInfo] = useState<EdgeCaseInfo>();
  const [edgeCaseModalVisible, setEdgeCaseModalVisible] = useState(false);

  const checkedInstallmentRegistration = useRef(false);
  const { persistPartnerData, getChosenPartner } = usePartnerData();
  const chosenPartner = getChosenPartner();
  

  const showErrorAppToast = (message: string) => {
    dispatch(setAppToast({ message, type: ToastType.ERROR, duration: 3000 }));
  };

  const handleVerifyIssueCta = async (action: string) => {
    setVerifyModalVisible(false);
    switch (action) {
      case 'reset_nfc':
        // TODO: Remove this after 10.12.0 is released all users
        if (appInfo?.platform === 'ZPA' && !isAppVersionGreaterThanOrEqual('10.12.0')) {
          //#region old reset NFC flow
          try {
            await postResetNfcApi();
            InfoModalService.showModal({
              screen: (
                <EdgeCaseModalUI
                  edgeCaseInfo={{
                    title: 'Bổ sung thông tin sinh trắc học',
                    description: 'Sử dụng CCCD có chip để bổ sung thông tin.',
                    ctas: [PredefinedCTA.REDIRECT_NFC_PROCESS],
                  }}
                  handleCtaAction={(_: string) => {
                    InfoModalService.hideModal();
                  }}
                />
              ),
              type: ModalType.BOTTOM_SHEET,
              bottomSheetProps: {
                title: '',
              },
            });
          } catch (e: any) {}
          //#endregion
          return;
        }
        setTimeout(async () => {
          await promptAuthChallengeFlow(Number(authSource), AuthChallengeType.ResetNFC);
        }, 500);
        break;
      case 'credit_score':
        launchCreditScoreDeeplink();
        break;
      case 'retry':
        await handleSubmit(handleSubmitData(bindingInfo?.basic_profile?.full_name))();
        break;
      case 'nfc':
        try {
          const result = await promptAuthChallengeFlow(Number(authSource), AuthChallengeType.NFC);
          if (result.status === UMStatus.Success) {
            await handleSubmitData(bindingInfo?.basic_profile?.full_name)();
          }
        } catch (e: any) {
          showErrorAppToast('Có lỗi xảy ra, vui lòng thử lại sau.');
        }
        break;
      case 'use_full_name_suggest':
        await handleSubmitData(bindingInfo?.basic_profile?.full_name_suggestion ?? '')();
        break;
      case 'back':
        closeMiniApp();
        break;
      case 'continue':
        onContinue();
        break;
      case 'update_kyc':
        try {
          showLoading();
          await postCheckKycWhitelist(UMSource.ADJUST_KYC_FROM_BNPL);
          launchKycUpdateDeeplink(UMSource.ADJUST_KYC_FROM_BNPL);
        } catch (e) {
          showErrorDialog('Có lỗi xảy ra, vui lòng thử lại sau.');
        } finally {
          hideLoading();
        }
        break;
      case 'liveness_detection':
        try {
          showLoading();
          //Workaround for liveness detection deeplink not working when called from modal (iOS)
          setTimeout(() => {
            launchLivenessDetectionDeeplink(UMSource.ADJUST_KYC_FROM_BNPL);
          }, 500);
        } catch (e) {
          showErrorDialog('Có lỗi xảy ra, vui lòng thử lại sau.');
        } finally {
          hideLoading();
        }
        break;
    }
  };

  useEffect(() => {
    setVerifyModalVisible(Boolean(foundIssue));
  }, [foundIssue]);

  useEffect(() => {
    setEdgeCaseModalVisible(Boolean(edgeCaseInfo));
  }, [edgeCaseInfo]);

  useEffect(() => {
    setOnBoardingId(hookOnboardingId);
  }, [hookOnboardingId]);

  useEffect(() => {
    type3Modal && trackBod2ResultDialogVisible({ result: 'TYPE3' });
  }, [type3Modal]);

  const [fullKycInfo, setFullKycInfo] = useState<boolean>();

  useEffect(() => {
    fullKycInfo !== undefined && active && trackBod2Visible({ load_from: loadFrom, enough_info: true });
  }, [active, fullKycInfo, loadFrom]);

  // This is a hack to force the hook below to run only once.
  // Now it run multiple times, which is wrong.
  // TODO: Refactor this part, also this file to organize the logic better. So many dangerous points.
  const initRef = useRef(true);
  useEffect(() => {
    if (!initRef.current) {
      return;
    }
    if (bindingInfo) {
      const { detail_profile } = bindingInfo;

      const mappedDetailProfile = {
        birthday: detail_profile.birthday,
        identityIssuedDate: detail_profile.id_issued_date,
        identityIssuedLocation: detail_profile.id_issued_location,
        address: detail_profile.permanent_address,
        temporaryAddress: detail_profile.permanent_address,
        sex: detail_profile.gender,
      };
      setFullKycInfo(isAllFieldsHaveValue(mappedDetailProfile));
      setOriginValues(mappedDetailProfile);
      setDefaultValues({ ...mappedDetailProfile, ...(storeUserData as Bod2_FormValues) });
      initRef.current = false;
    }
  }, [bindingInfo, storeUserData]);

  useEffect(() => {
    setValue('sex', defaultValues?.sex || 'MALE');
    setValue('birthday', defaultValues?.birthday || '');
    setValue('identityIssuedDate', defaultValues?.identityIssuedDate || '');
    setValue('identityIssuedLocation', defaultValues?.identityIssuedLocation || '');
    setPlaceOfIssue(defaultValues?.identityIssuedLocation || '');
    setValue('address', defaultValues?.address || '');
    setValue('temporaryAddress', defaultValues?.temporaryAddress || '');
    if (defaultValues?.address !== defaultValues?.temporaryAddress && !enteringAddress) {
      setShowTemporaryAddress(true);
    }
  }, [defaultValues]);

  const validateBirthday = (value: Bod2_FormValues['birthday']) => {
    const date = parseDate(value);
    const now = new Date();
    if (isAfter(date, new Date())) {
      return 'Ngày sinh phải ở trong quá khứ';
    }
    if (differenceInYears(now, date) < 18) {
      return 'Bạn chưa đủ 18 tuổi';
    }
    if (isValidDate(value)) {
      return 'Ngày sinh không hợp lệ';
    }
  };

  const validateIdentityIssuedDate = (value: Bod2_FormValues['identityIssuedDate']) => {
    const date = parseDate(value);
    if (isAfter(date, new Date())) {
      return 'Ngày cấp CMND/CCCD phải ở trong quá khứ';
    }
    if (isValidDate(value)) {
      return 'Ngày cấp CMND/CCCD không hợp lệ';
    }
  };

  const renderCommonErrorMessage = (error?: FieldError) => {
    if (!error) {
      return;
    }
    if (error.message) {
      return error.message;
    }
    switch (error.type) {
      case 'required':
        return 'Thông tin không được bỏ trống';
      default:
        return `Lỗi không xác định: ${error.type}`;
    }
  };

  const handleSetPlaceOfIssue = (value: string) => {
    setPlaceOfIssue(value);
    setPlaceOfIssueModalVisible(false);
  };

  const handleUpdateTemporaryAddress = (value: string) => {
    setValue('temporaryAddress', value);
    setStoreUserData({ ...defaultValues, temporaryAddress: value });
    setUpdateAddressModalVisible(false);
  };

  const { getValueForResourceType, fetchChoices } = usePersonalExtraDataController();

  useEffect(() => {
    (async () => {
      await fetchChoices();
    })();
  }, [fetchChoices]);

  const [submitting, setSubmitting] = useState(false);
  const submittingRef = useRef(submitting);
  const handleSubmitData = (fullName: string) => async () => {
    if (submittingRef.current) {
      return;
    }

    if (!requireInfoInputRef?.current?.validateInput()) {
      return;
    }

    setSubmitting(true);
    submittingRef.current = true;
    showLoading();
    const values = getValues();
    try {
      const result = await postDetailInfoApi({
        full_name: fullName,
        request_id: onBoardingId,
        birthday: values.birthday,
        id_issued_date: values.identityIssuedDate,
        id_issued_location: values.identityIssuedLocation,
        permanent_address: values.address,
        temp_residence_address: values.temporaryAddress || values.address,
        gender: values.sex,
        occupation: getValueForResourceType('OCCUPATION'),
        employment_status: getValueForResourceType('EMPLOYMENT_STATUS'),
        job_title: getValueForResourceType('JOB_TITLE'),
        source_of_fund: getValueForResourceType('SOURCE_OF_FUND'),
        monthly_income: getValueForResourceType('INCOME'),
        education: getValueForResourceType('EDUCATION'),
        living_city: getValueForResourceType('CITY'),
        loan_purpose: getValueForResourceType('LOAN_PURPOSE'),
        agreements: { installment_registration: checkedInstallmentRegistration.current },
      });
      if (result.partner_account_exist) {
        setType3Modal(true);
        return;
      }
      if (result.issue_existed && result.issue_details) {
        setFoundIssue(mapIssue(result.issue_details.name, result.issue_details.description, result.issue_details.code));
        return;
      }
      if (result.edge_case_info?.ctas && result.edge_case_info?.title && result.edge_case_info?.description) {
        setEdgeCaseInfo(result.edge_case_info);
        return;
      }
      if(!!checkedInstallmentRegistration.current && chosenPartner?.partner_code) {
        const updatedData = {
          ...chosenPartner,
          allows_installment_registration: !!checkedInstallmentRegistration.current,
        };
        persistPartnerData(chosenPartner?.partner_code, updatedData);
      }

      const { authentication_type } = await postInitOnboardingSignContractApi(hookOnboardingId);
      if (authentication_type === SignMethod.FACE) {
        onContinue();
      }
    } catch (e: any) {
      const isHandled = await handleOnboardingRejection({ triggerLocation: 'bod2' });
      if (!isHandled) {
        trackBod2SubmitError({ error_code: e.code });
        showErrorAppToast(e?.message || 'Có lỗi xảy ra, vui lòng thử lại sau.');
      }
    } finally {
      hideLoading();
      setSubmitting(false);
      submittingRef.current = false;
    }
  };

  const fullName = bindingInfo?.basic_profile?.full_name;

  useEffect(() => {
    if (action) {
      if (action === 'submit') {
        handleSubmit(handleSubmitData(bindingInfo?.basic_profile?.full_name))();
      }
    }
  }, [action]);

  return (
    <>
      <FullScreenFormLayout
        testID={'bod2-tab'}
        style={styles.root}
        contentStyles={{ paddingHorizontal: 0 }}
        actionsStyles={rewardBannerStyle.action}
        content={
          <Fragment>
            <Spacer height={12} />
            <View style={{ backgroundColor: AppColors.background, paddingHorizontal: 16 }}>
              <PersonalInfoButton
                onPress={() => {
                  const bindingData = [
                    { title: 'Số điện thoại', value: bindingInfo?.basic_profile?.phone_number || '' },
                    { title: 'CCCD/CMND', value: bindingInfo?.detail_profile?.id_number || '' },
                    { title: 'Ngày cấp', value: bindingInfo?.detail_profile?.id_issued_date || '' },
                    { title: 'Ngày hết hạn', value: bindingInfo?.detail_profile?.id_expired_date || '--' },
                    { title: 'Nơi cấp', value: bindingInfo?.detail_profile?.id_issued_location || '--' },
                    { title: 'Ngày sinh', value: bindingInfo?.detail_profile?.birthday || '' },
                    { title: 'Họ tên', value: bindingInfo?.detail_profile?.full_name || '' },
                    { title: 'Giới tính', value: bindingInfo?.detail_profile?.gender === 'MALE' ? 'Nam' : 'Nữ' },
                    { title: 'Quốc tịch', value: bindingInfo?.detail_profile?.nationality || '--' },
                    { title: 'Hình chân dung', value: 'Trong CCCD' },
                    { title: 'Chứng minh cũ', value: bindingInfo?.detail_profile?.old_id_number || '--' },
                  ];
                  navigation.navigate(ScreenKey.PersonalExtraDataScreen, {
                    bindingData,
                    showDetailInfo: true,
                    partnerCode: PartnerCode.CIMB,
                  });
                }}
                left="Thông tin cá nhân"
                right={fullName}
              />
              <Spacer height={16} />
              {!originValues?.sex && (
                <Controller
                  control={control}
                  name="sex"
                  rules={{
                    required: true,
                  }}
                  defaultValue={defaultValues?.sex}
                  render={({ field: { value, onChange } }) => (
                    <SelectSex
                      style={styles.sex}
                      value={value}
                      onChange={selected => {
                        trackBod2InputGender({ gender: selected });
                        setStoreUserData({ ...defaultValues, sex: selected });
                        onChange(selected);
                      }}
                    />
                  )}
                />
              )}
            </View>

            <RequireAdditionalInfo
              requireResources={['OCCUPATION', 'JOB_TITLE', 'CITY', 'INCOME']}
              ref={requireInfoInputRef}
            />
            <View style={styles.section}>
              {!originValues?.birthday && (
                <Controller
                  control={control}
                  name="birthday"
                  defaultValue={defaultValues?.birthday}
                  rules={{
                    validate: validateBirthday,
                  }}
                  render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                    <DateMaskInput
                      label="Ngày tháng năm sinh"
                      editable={true}
                      value={value}
                      defaultValue={value}
                      testID="birthday"
                      error={renderCommonErrorMessage(error)}
                      keyboardType="numeric"
                      onChangeText={text => {
                        setStoreUserData({ ...defaultValues, birthday: text });
                        onChange(text);
                      }}
                      onBlur={() => {
                        onBlur();
                      }}
                      style={styles.input}
                    />
                  )}
                />
              )}

              {!originValues?.identityIssuedDate && (
                <Controller
                  control={control}
                  name="identityIssuedDate"
                  defaultValue={defaultValues?.identityIssuedDate}
                  rules={{
                    validate: validateIdentityIssuedDate,
                  }}
                  render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                    <DateMaskInput
                      label="Ngày cấp CMND/CCCD"
                      editable={true}
                      value={value}
                      defaultValue={value}
                      testID="identityIssuedDate"
                      error={renderCommonErrorMessage(error)}
                      keyboardType="numeric"
                      onFocus={() => {
                        trackBod2TapIdentityIssueDate();
                      }}
                      onChangeText={text => {
                        setStoreUserData({ ...defaultValues, identityIssuedDate: text });
                        onChange(text);
                      }}
                      onBlur={() => {
                        onBlur();
                      }}
                      style={styles.input}
                    />
                  )}
                />
              )}

              {!originValues?.identityIssuedLocation && (
                <Controller
                  control={control}
                  name="identityIssuedLocation"
                  rules={{
                    required: true,
                  }}
                  defaultValue={defaultValues?.identityIssuedLocation}
                  render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                    <SelectInput
                      style={styles.input}
                      label="Nơi cấp CMND/CCCD"
                      value={placeOfIssue}
                      editable={true}
                      error={renderCommonErrorMessage(error)}
                      onPress={() => {
                        trackBod2TapIdentityIssuePlace();
                        setPlaceOfIssueModalVisible(true);
                        if (placeOfIssue === '') {
                          setPlaceOfIssue(value);
                        }
                      }}
                      onChange={text => {
                        setStoreUserData({ ...defaultValues, identityIssuedLocation: text });
                        onChange(text);
                        onBlur();
                      }}
                    />
                  )}
                />
              )}

              <Controller
                control={control}
                name="address"
                rules={{
                  required: true,
                  minLength: {
                    value: 1,
                    message: 'Vui lòng điền tối thiểu 1 ký tự.',
                  },
                  maxLength: {
                    value: 500,
                    message: 'Vui lòng điền tối đa 500 ký tự.',
                  },
                }}
                defaultValue={defaultValues?.address}
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <AppTextInput
                    label="Địa chỉ thường trú"
                    value={value}
                    editable={!defaultValues?.address}
                    focusable={!defaultValues?.address}
                    defaultValue={value}
                    testID="address"
                    error={renderCommonErrorMessage(error)}
                    onChangeText={text => {
                      setStoreUserData({ ...defaultValues, address: text });
                      onChange(text);
                    }}
                    onFocus={() => {
                      setEnteringAddress(true);
                      trackBod2TapAddress({ address_type: 'permanent' });
                    }}
                    onBlur={() => {
                      setEnteringAddress(false);
                      onBlur();
                    }}
                    style={styles.input}
                  />
                )}
              />

              {showTemporaryAddress && (
                <Controller
                  control={control}
                  name="temporaryAddress"
                  rules={{
                    required: true,
                    minLength: {
                      value: 1,
                      message: 'Vui lòng điền tối thiểu 1 ký tự.',
                    },
                    maxLength: {
                      value: 500,
                      message: 'Vui lòng điền tối đa 500 ký tự.',
                    },
                  }}
                  defaultValue={defaultValues?.temporaryAddress}
                  render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                    <AppTextInput
                      label="Địa chỉ tạm trú"
                      value={value}
                      defaultValue={value}
                      testID="temporaryAddress"
                      error={renderCommonErrorMessage(error)}
                      onFocus={() => {
                        trackBod2TapAddress({ address_type: 'temporary' });
                      }}
                      onChangeText={text => {
                        setStoreUserData({ ...defaultValues, temporaryAddress: text });
                        onChange(text);
                      }}
                      onBlur={() => {
                        onBlur();
                      }}
                      style={styles.input}
                    />
                  )}
                />
              )}

              {!showTemporaryAddress && (
                <AppText color={Colors.text} style={styles.newAddress}>
                  Địa chỉ tạm trú giống địa chỉ thường trú{' '}
                  <LinkButton
                    testID="btn-input-temp-address"
                    onPress={() => {
                      setShowTemporaryAddress(true);
                      setUpdateAddressModalVisible(true);
                      trackBod2AddNewAddress();
                    }}>
                    Nhập địa chỉ mới
                  </LinkButton>
                </AppText>
              )}
              <InstallmentRegistrationCheckbox
                style={styles.confirmCheckbox}
                onChangeValue={checked => {
                  checkedInstallmentRegistration.current = checked;
                }}
              />
              <CheckBox
                testID="confirm-checkbox"
                style={styles.confirmCheckbox}
                value={checkedConfirm}
                onChangeValue={checked => {
                  trackBod2TapCheckbox({ value: checked ? 'check' : 'uncheck' });
                  setCheckedConfirm(checked);
                }}
                title={<AppText>Tôi xác nhận</AppText>}
              />
              <InfoBox
                lines={[
                  <AppText key={0}>Tôi là công dân đang sinh sống tại Việt Nam</AppText>,
                  <AppText key={1}>Tôi không nhận uỷ thác của Tổ chức Uỷ thác nước ngoài</AppText>,
                  <AppText key={2}>Tôi không phải là công dân đang sống hoặc giữ thẻ thường trú tại Hoa Kỳ</AppText>,
                  <AppText key={3}>Thông tin đăng ký là chính xác</AppText>,
                  <AppText key={4}>
                    Tài khoản thanh toán sẽ được mở kèm nếu đáp ứng đủ điều kiện trong trường hợp Tài Khoản Trả Sau mở
                    thành công hoặc bị từ chối.
                  </AppText>,
                  <AppText key={5}>Liên kết tài khoản với Zalopay</AppText>,
                  <AppText key={6}>Tôi có thể nhận được cuộc gọi từ CIMB để xác minh thông tin đăng ký.</AppText>,
                  <AppText key={7}>Quét lại Căn cước công dân để cập nhật thông tin nếu cần thiết</AppText>,
                ]}
              />
            </View>

            <PlaceOfIssueFilterModal
              value={placeOfIssue}
              visible={placeOfIssueModalVisible}
              onChange={handleSetPlaceOfIssue}
              onClose={() => handleSetPlaceOfIssue(placeOfIssue)}
            />

            <UpdateAddressModal
              value={defaultValues?.temporaryAddress || ''}
              visible={updateAddressModalVisible}
              onChange={handleUpdateTemporaryAddress}
              onClose={() => setUpdateAddressModalVisible(false)}
            />
            <Type3Modal
              visible={type3Modal}
              onCloseButtonPressed={() => trackBod2ResultDialogClose({ result: 'TYPE3' })}
              onRequestClose={() => setType3Modal(false)}
              onContinue={async () => {
                setType3Modal(false);
                await handleSubmitData(bindingInfo?.basic_profile?.full_name)();
              }}
            />
          </Fragment>
        }
        actions={
          <Fragment>
            <View>
              <RewardBanner
                onLoad={(metadata: any) => (active ? trackBod2RewardBannerLoad(metadata) : null)}
                onPressAdBanner={trackBod2RewardBannerClickBanner}
                onPressShowAd={trackBod2RewardBannerClickShowAd}
                partner={PartnerCode.CIMB.toLocaleLowerCase()}
                bannerKey={RewardBannerKey.Bod2}
              />
            </View>
            <View>
              <ActionBottom
                style={rewardBannerStyle?.actionBtn}
                testID="submit-bod2"
                disabled={!checkedConfirm || submitting}
                title="Tiếp tục"
                onBack={onBack}
                onSubmit={() => {
                  trackBod2TapCta({
                    metadata: `Tôi là công dân đang sinh sống tại Việt Nam
                    Tôi không nhận uỷ thác của Tổ chức Uỷ thác nước ngoài
                    Tôi không phải là công dân đang sống hoặc giữ thẻ thường trú tại Hoa Kỳ
                    Thông tin đăng ký là chính xác
                    Tài khoản thanh toán sẽ được mở kèm nếu đáp ứng đủ điều kiện trong trường hợp Tài Khoản Trả Sau mở thành công hoặc bị từ chối.
                    Liên kết tài khoản với Zalopay
                    Tôi có thể nhận được cuộc gọi từ CIMB để xác minh thông tin đăng ký.
                    Quét lại Căn cước công dân để cập nhật thông tin nếu cần thiết`,
                  });
                  handleSubmit(handleSubmitData(bindingInfo?.basic_profile?.full_name))();
                }}
              />
            </View>
          </Fragment>
        }
      />
      {foundIssue !== undefined && (
        <VerifyIssueModal
          location="application"
          issue={foundIssue}
          visible={verifyModalVisible}
          handleCtaAction={handleVerifyIssueCta}
        />
      )}
      {edgeCaseInfo !== undefined && (
        <EdgeCaseModal location="application" edgeCaseInfo={edgeCaseInfo} visible={edgeCaseModalVisible} />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
    ...Platform.select({
      web: {
        height: 'calc(100vh - 38px)',
      },
    }),
  },
  input: {
    marginBottom: 12,
  },
  newAddress: {
    marginTop: 4,
    marginBottom: 12,
  },
  sex: {
    marginBottom: 22,
  },
  confirmCheckbox: {
    marginTop: 10,
    marginBottom: 14,
  },
  section: { backgroundColor: AppColors.background, padding: 16 },
});

const isAllFieldsHaveValue = (mapping: { [key: string]: any }) => {
  for (const key in mapping) {
    if (mapping[key] === undefined || mapping[key] === null) {
      return false;
    }
  }
  return true;
};

//#region
const PersonalInfoButton: FC<{ left: string; right: string; onPress: () => void }> = ({ left, right, onPress }) => {
  const styles = personalInfoButtonStyles;
  return (
    <TouchableOpacity onPress={onPress} style={[StyleUtils.rowStretchBetween, styles.root]}>
      <AppText>{left}</AppText>
      <View style={StyleUtils.flexRow}>
        <AppText>{right}</AppText>
        <Spacer width={8} />
        <ChevronIcon direction="right" />
      </View>
    </TouchableOpacity>
  );
};

const personalInfoButtonStyles = StyleSheet.create({
  root: {
    borderWidth: 1,
    borderColor: Colors.primary2,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 15,
  },
});
//#endregion
