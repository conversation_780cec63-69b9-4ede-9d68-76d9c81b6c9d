import React, { FC, useEffect, useState } from 'react';
import { Platform, View } from 'react-native';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { StyleUtils, windowWidth } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { getBottomSafe } from 'bnpl-shared/src/utils';
import { images } from 'bnpl-shared/src/res';
import { AdsInventoryId, AuthChallengeType, RewardBannerKey, UMSource, UMStatus } from 'bnpl-shared/src/types';
import { usePromptAuthChallengeFlow } from 'bnpl-shared/src/shared/ZaloPayModules';
import { AppColors, PartnerCode } from 'bnpl-shared/src/constants';
import { setAppToast, ToastType } from 'bnpl-shared/src/redux/appToastReducer';
import { useDispatch } from 'react-redux';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import ContractBottomSheet from 'bnpl-shared/src/screens/onboarding-flow/AddInformationScreen/components/ContractBottomSheet';
import {
  trackFaceChallengeRewardBannerClickBanner,
  trackFaceChallengeRewardBannerClickShowAd,
  trackFaceChallengeRewardBannerLoad,
  trackFaceChallengeCallbackFromUM,
  trackFaceChallengeLoad,
  trackFaceChallengePressContract,
  trackFaceChallengePressMainCTA,
  trackFaceChallengePressPrivacy,
} from 'bnpl-shared/src/screens/onboarding-flow/AddInformationScreen/tracking';
import RenewContractBottomSheet from 'bnpl-shared/src/features/renew_overdraft/screens/RenewSignSubmitScreen/RenewContractBottomSheet';
import RewardBanner from 'bnpl-shared/src/features/reward_banner';
import usePreparedMerchantAdId from 'bnpl-shared/src/hooks/usePreparedMerchantAdId';
import { useAuthChallengeSource } from 'bnpl-shared/src/hooks/useAuthChallengeSource';
import { usePartnerData } from 'bnpl-shared/src/features/multipartner_integration/helpers';
import InstallmentTermBottomSheet from '../components/InstallmentTermBottomSheet';

export const FaceChallengeSubScreen: FC<{
  flow: 'renew' | 'onboard';
  active?: boolean;
  onContinue: (request_id: string) => void;
  onTermPress?: () => void;
}> = ({ onContinue, onTermPress, flow, active }) => {
  const dispatch = useDispatch();
  const { promptAuthChallengeFlow } = usePromptAuthChallengeFlow();
  const authSource = useAuthChallengeSource();
  const [contractBottomSheetVisible, setContractBottomSheetVisible] = useState(false);
  const [installmentTermBottomSheetVisible, setInstallmentTermBottomSheetVisible] = useState(false);

  const { getChosenPartner } = usePartnerData();
  const chosenPartner = getChosenPartner();
  const allowInstallmentRegistration =
    chosenPartner?.partner_code === PartnerCode.CIMB && chosenPartner?.allows_installment_registration;

  const { isAdIdPrepared } = usePreparedMerchantAdId(
    flow === 'onboard' ? AdsInventoryId.BOD_2 : AdsInventoryId.RENEW_FACE_AUTHEN,
  );

  const showErrorAppToast = (message: string) => {
    dispatch(setAppToast({ message, type: ToastType.ERROR, duration: 3000 }));
  };

  const handleFaceChallengeFlow = async () => {
    trackFaceChallengePressMainCTA(flow);
    try {
      const result = await promptAuthChallengeFlow(Number(authSource), AuthChallengeType.FaceAuth);
      if (result.status === UMStatus.Success && result.resultData) {
        trackFaceChallengeCallbackFromUM(flow, UMStatus[result.status].toLowerCase());
        onContinue(result.resultData);
      }
    } catch (e: any) {
      console.log(e);
      if (e !== 'failed') {
        showErrorAppToast(e?.message || 'Có lỗi xảy ra, vui lòng thử lại sau.');
      }
    }
  };

  const onInstallmentTermPress = () => {
    setInstallmentTermBottomSheetVisible(true);
  };

  const closeInstallmentTermBottomSheet = () => {
    setInstallmentTermBottomSheetVisible(false);
  };

  useEffect(() => {
    if (!active || !flow) {
      return;
    }
    trackFaceChallengeLoad(flow);
  }, [active, flow]);

  const footerStyle =
    flow === 'onboard'
      ? styles.footer
      : {
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          paddingVertical: 16,
          backgroundColor: AppColors.white,
        };

  return (
    <View testID="face-challenge-sub-screen" style={styles.content}>
      <Spacer height={16} />
      <AppImage testID={'img-face'} source={images.ImageFaceChallengeScreen} height={200} width={280} />
      <Spacer height={16} />
      <View style={styles.text}>
        <AppText bold size={16} height={20}>
          Bấm &quot;Xác thực ngay&quot; để ký hợp đồng
        </AppText>
      </View>
      <Spacer height={20} />
      <View style={{ width: windowWidth, position: 'absolute', bottom: 0, left: 0, right: 0 }}>
        <AppText style={[StyleUtils.flexOne, { margin: 16 }]}>
          Bằng cách nhấn &quot;Xác thực ngay&quot;, tôi đã đọc và đồng ý với nội dung:{' '}
          <LinkButton
            key="contract-link"
            testID="contract-link"
            onPress={() => {
              trackFaceChallengePressContract(flow);
              setContractBottomSheetVisible(true);
            }}>
            Hợp đồng mở và sử dụng tài khoản
          </LinkButton>
          ,{' '}
          <LinkButton
            key="term-link"
            testID="term-link"
            onPress={() => {
              onTermPress?.();
              trackFaceChallengePressPrivacy(flow);
            }}>
            Điều khoản và điều kiện sản phẩm Tài khoản trả sau
          </LinkButton>
          {allowInstallmentRegistration ? (
            <>
              ,{' '}
              <LinkButton
                key="installment-contract-link"
                testID="installment-contract-link"
                onPress={() => {
                  onInstallmentTermPress?.();
                  trackFaceChallengePressPrivacy(flow);
                }}>
                trả góp của CIMB
              </LinkButton>
            </>
          ) : null}
        </AppText>
        <View style={footerStyle}>
          {isAdIdPrepared && (
            <RewardBanner
              onLoad={(metadata: any) => (active ? trackFaceChallengeRewardBannerLoad(metadata) : null)}
              onPressAdBanner={trackFaceChallengeRewardBannerClickBanner}
              onPressShowAd={trackFaceChallengeRewardBannerClickShowAd}
              partner={PartnerCode.CIMB.toLocaleLowerCase()}
              bannerKey={flow === 'onboard' ? RewardBannerKey.Bod2 : RewardBannerKey.RenewFaceAuthen}
            />
          )}
          <Spacer height={16} />
          <AppButton
            testID={'btn-continue'}
            buttonStyle={[styles.button]}
            title="Xác thực ngay"
            variant={'contained'}
            onPress={handleFaceChallengeFlow}
          />
        </View>
      </View>
      {flow === 'onboard' && (
        <>
          <ContractBottomSheet
            visible={contractBottomSheetVisible}
            allowInstallmentRegistration={allowInstallmentRegistration}
            onRequestClose={() => setContractBottomSheetVisible(false)}
          />
          {allowInstallmentRegistration ? (
            <InstallmentTermBottomSheet
              visible={installmentTermBottomSheetVisible}
              onRequestClose={() => closeInstallmentTermBottomSheet()}
            />
          ) : null}
        </>
      )}
      {flow === 'renew' && (
        <RenewContractBottomSheet
          visible={contractBottomSheetVisible}
          onRequestClose={() => setContractBottomSheetVisible(false)}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
    ...Platform.select({
      web: {
        height: 'calc(100vh - 38px)',
      },
    }),
    paddingHorizontal: 16,
    alignItems: 'center',
    paddingBottom: getBottomSafe(),
    backgroundColor: AppColors.background2,
  },
  text: {
    paddingHorizontal: 8,
    alignItems: 'center',
  },
  button: {
    width: 340,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  footer: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    backgroundColor: AppColors.white,
  },
});
