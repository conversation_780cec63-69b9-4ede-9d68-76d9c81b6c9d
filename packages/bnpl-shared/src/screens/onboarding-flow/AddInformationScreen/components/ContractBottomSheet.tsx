import React, { FC, useEffect, useState } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { trackAfterBod2ReadyToSign } from '../SignContractSubScreen/tracking';
import { AppModal } from 'bnpl-shared/src/local/AppModal';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import { StyleUtils, windowHeight } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { WebView } from 'react-native-webview';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { useOnboardingId } from 'bnpl-shared/src/hooks/useOnboardingId';
import { trackContractLoadSucceed } from 'bnpl-shared/src/screens/onboarding-flow/AddInformationScreen/SignContractSubScreen/tracking';
import { getBindingContractApi } from 'bnpl-shared/src/api/getBindingContractApi';
import Tabs from 'bnpl-shared/src/components/Tabs';

const tabsConfig = [
  { value: 'bnpl', label: 'Tài khoản trả sau' },
  { value: 'installment', label: 'Trả góp' },
];

export enum TabKey  {
  bnpl = 'bnpl',
  installment = 'installment',
};

const ContractBottomSheet: FC<{ visible: boolean; onRequestClose: () => void; intervalTime?: number, allowInstallmentRegistration?: boolean }> = ({
  visible,
  onRequestClose,
  intervalTime = 5000,
  allowInstallmentRegistration = false,
}) => {
  const onboardingId = useOnboardingId();
  const [firstFetch, setFirstFetch] = useState(false);
  const [contractLink, setContractLink] = useState<string | undefined>();
  const [activeTab, setActiveTab] = useState<string>(TabKey.bnpl);
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  useEffect(() => {
    let pollingId: any;

    if (!visible || !onboardingId || contractLink) {
      return;
    }

    const fetchBindingContract = async () => {
      try {
        const { contract_template } = await getBindingContractApi({
          request_id: onboardingId,
        });
        if (contract_template) {
          setContractLink(contract_template);
          trackAfterBod2ReadyToSign();
          trackContractLoadSucceed();
          clearInterval(pollingId);
        }
      } catch (error) {}
    };

    const getBindingContract = async () => {
      if (firstFetch) {
        pollingId = setInterval(fetchBindingContract, intervalTime);
        return;
      }

      await fetchBindingContract();
      setFirstFetch(true);
    };

    getBindingContract();

    return () => clearInterval(pollingId);
  }, [onboardingId, contractLink, firstFetch, intervalTime, visible]);


  const renderContent = (tab: string) => {
      if (!visible) {
        return null;
      }
      if (tab === TabKey.bnpl) {
        return (
          <>
            {contractLink ? (
              <WebView source={{ uri: contractLink }} />
            ) : (
              <View style={[StyleUtils.centered, styles.bodyStyle]}>
                <ActivityIndicator />
                <Spacer height={8} />
                <AppText size={12}>Đang tải hợp đồng</AppText>
              </View>
            )}
          </>
        );
      } else {
        return (
          <>
            <WebView
              style={styles.installmentContract}
              source={{ uri: 'https://simg.zalopay.com.vn/fs/bnpl/document/installment-temp-contract/index.html' }}
            />
          </>
        );
      }
    }

  return (
    <AppModal transparent visible={visible} onRequestClose={onRequestClose}>
      <BottomSheetLayout
        bodyStyle={styles.bodyStyle}
        containerStyle={StyleUtils.flexOne}
        title="Xem hợp đồng"
        onRequestClose={onRequestClose}
        content={
          <>
            {allowInstallmentRegistration ? (
              <Tabs
                tabs={tabsConfig}
                activeTab={activeTab}
                onChange={handleTabChange}
              />
            ) : null}
            {renderContent(activeTab)}
          </>
        }
      />
    </AppModal>
  );
};

const styles = StyleSheet.create({
  bodyStyle: {
    height: windowHeight - 200,
  },
  installmentContract: {
    padding: 10,
  }
});

export default ContractBottomSheet;
