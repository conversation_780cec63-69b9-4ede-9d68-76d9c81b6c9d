import React, { FC } from 'react';
import { AppModal } from 'bnpl-shared/src/local/AppModal';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import { StyleUtils } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { View } from 'react-native';
import { AppButton, AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { Spacer } from 'bnpl-shared/src/components/Spacer';
import { Divider } from 'bnpl-shared/src/components/Divider';
import { images } from 'bnpl-shared/src/res';

const InstallmentIntroBottomSheet: FC<{ visible: boolean; onRequestClose: () => void }> = ({
  visible,
  onRequestClose,
}) => {
  if (!visible) {
    return null;
  }

  return (
    <AppModal transparent visible={visible} onRequestClose={onRequestClose}>
      <BottomSheetLayout
        bodyStyle={styles.bodyStyle}
        containerStyle={StyleUtils.flexOne}
        title="Giới thiệu sản phẩm Trả góp"
        onRequestClose={onRequestClose}
        content={
          <View style={styles.root}>
            <View style={styles.iconInstallmentWrapper}>
              <AppImage source={images.ImageIconIntroInstallment} width={106} height={119} />
            </View>
            <Spacer height={24} />
            <View style={styles.item}>
              <AppImage source={images.IconGeneralSecurityCheck} width={24} height={24}/>
              <Spacer width={12} />
              <AppText size={16} height={20}>
                Hạn mức đến 30 triệu
              </AppText>
            </View>
            <Divider variant='line' style={styles.divider}/>
            <View style={styles.item}>
              <AppImage source={images.IconServiceTransferMoney} width={24} height={24}/>
              <Spacer width={12} />
              <AppText size={16} height={20}>
                Trả góp từ 500.000đ, linh hoạt đến 12 kỳ
              </AppText>
            </View>
            <Divider variant='line' style={styles.divider}/>
            <View style={styles.item}>
              <AppImage source={images.IconGeneralTime} width={24} height={24}/>
              <Spacer width={12} />
              <AppText size={16} height={20}>
                Không dùng, không mất phí
              </AppText>
            </View>
            <Divider variant='line' style={styles.divider}/>
            <View style={styles.item}>
              <AppImage source={images.IconGeneralMoreCircle} width={24} height={24}/>
              <Spacer width={12} />
              <AppText size={16} height={20}>
                Đa dạng ngành hàng dịch vụ: hoá đơn, mua sắm, du lịch, giải trí,...
              </AppText>
            </View>
            <Spacer height={24} />
             <View style={[StyleUtils.flexRow, StyleUtils.centered]}>
                <AppText size={12} height={16}>
                  Cung cấp bởi
                </AppText>
                <Spacer width={4} />
                <AppImage source={images.IconLogoCIMBFull} height={37} width={87} />
              </View>
              <AppButton variant="contained" title="Đóng" onPress={onRequestClose} />

          </View>
        }
      />
    </AppModal>
  );
};

const styles = StyleSheet.create({
  bodyStyle: {
    height: 535,
  },
  root: {
    padding: 16,
  },
  iconInstallmentWrapper: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingVertical: 12,
  },
  divider: {
    width: "100%"
  }
});

export default InstallmentIntroBottomSheet;
