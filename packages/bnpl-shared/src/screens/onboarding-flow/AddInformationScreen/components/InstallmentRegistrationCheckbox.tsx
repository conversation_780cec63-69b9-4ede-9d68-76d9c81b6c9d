import { useABTesting } from 'bnpl-shared/src/hooks/useABTesting';
import { CheckBox } from 'bnpl-shared/src/shared/CheckBox/CheckBox';
import { LinkButton } from 'bnpl-shared/src/shared/LinkButton';
import { AppImage, AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { ABTestingGroup, ExperimentName } from 'bnpl-shared/src/types';
import React, { useEffect, useState } from 'react';
import { ViewStyle } from 'react-native';
import InstallmentIntroBottomSheet from './InstallmentIntroBottomSheet';
import { images } from 'bnpl-shared/src/res';
import { AppColors } from 'bnpl-shared/src/constants';

const whiteListKey = ExperimentName.INSTALLMENT_REGISTRATION_CHECKBOX;

export const InstallmentRegistrationCheckbox = ({
  onChangeValue,
  style,
}: {
  onChangeValue: (checked: boolean) => void;
  style?: ViewStyle;
}) => {
  const { fetchABTestResult, isInWhiteList } = useABTesting();
  const [checkedInstallmentRegistration, setCheckedInstallmentRegistration] = useState(false);
  const shouldShowInstallmentRegistrationCheckBox = isInWhiteList(whiteListKey);
  const [installmentIntroVisible, setInstallmentIntroVisible] = useState(false);

  const handleOpenInstallmentIntroBS = () => {
    setInstallmentIntroVisible(true);
  }
  const handleCloseInstallmentIntroBS = () => {
    setInstallmentIntroVisible(false);
  }
  useEffect(() => {
    onChangeValue?.(checkedInstallmentRegistration);
  }, [checkedInstallmentRegistration, onChangeValue]);

  useEffect(() => {
    (async () => {
      const abTestingResult = await fetchABTestResult(whiteListKey);
      const shouldAutoCheckInstallmentRegistrationCheckbox =
        shouldShowInstallmentRegistrationCheckBox &&
        abTestingResult?.toLowerCase() === ABTestingGroup.Variation_1.toLowerCase();
      setCheckedInstallmentRegistration(!!shouldAutoCheckInstallmentRegistrationCheckbox);
    })();
  }, []);

  return (
    <>
      {shouldShowInstallmentRegistrationCheckBox ? (
        <CheckBox
          testID="installment-registration-checkbox"
          style={style}
          value={checkedInstallmentRegistration}
          onChangeValue={checked => {
            setCheckedInstallmentRegistration(checked);
          }}
          title={<AppText>Tôi đồng ý đăng ký sử dụng thêm dịch vụ{' '}
                    <LinkButton
                      key="installment-contract-link"
                      testID="installment-contract-link"
                      onPress={() => {
                        handleOpenInstallmentIntroBS();
                      }}>
                      trả góp CIMB
                      <AppImage source={{ uri: images.IconInfolCircleNoFill }} tintColor={AppColors.primary} width={16} height={16}  />
                    </LinkButton>
                  </AppText>}
        />
      ) : null}
      <InstallmentIntroBottomSheet visible={installmentIntroVisible} onRequestClose={handleCloseInstallmentIntroBS} />
    </>
  );
};
