import React, { FC } from 'react';
import { AppModal } from 'bnpl-shared/src/local/AppModal';
import { BottomSheetLayout } from 'bnpl-shared/src/shared/BottomSheet/BottomSheetLayout';
import { StyleUtils, windowHeight } from 'bnpl-shared/src/shared/styles/StyleUtils';
import { StyleSheet } from 'bnpl-shared/src/shared/styles/StyleSheet';
import { FileViewer } from 'bnpl-shared/src/components/FileViewer';
import { ScrollView } from 'react-native';

const InstallmentTnC = 'https://simg.zalopay.com.vn/fs/Installment/pdf/Installment_TnC_Effective_28102024.pdf';

const InstallmentTermBottomSheet: FC<{ visible: boolean; onRequestClose: () => void }> = ({
  visible,
  onRequestClose,
}) => {
  if (!visible) {
    return null;
  }

  return (
    <AppModal transparent visible={visible} onRequestClose={onRequestClose}>
      <BottomSheetLayout
        bodyStyle={styles.bodyStyle}
        containerStyle={StyleUtils.flexOne}
        title="Điều khoản và điều kiện sản phẩm"
        onRequestClose={onRequestClose}
        content={
          <ScrollView showsVerticalScrollIndicator={false}>
            <FileViewer url={InstallmentTnC} type={'pdf'} />
          </ScrollView>
        }
      />
    </AppModal>
  );
};

const styles = StyleSheet.create({
  bodyStyle: {
    height: windowHeight - 200,
  },
});

export default InstallmentTermBottomSheet;
