import React from 'react';
import { render, fireEvent, act } from '@testing-library/react-native';
import { BottomSheetLayout } from './BottomSheetLayout';
import { AppText } from '../react-native-customized';

// Mock Animated
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  const mockAnimated = {
    ...RN.Animated,
    timing: jest.fn(() => ({
      start: jest.fn((callback) => callback && callback()),
    })),
    parallel: jest.fn(() => ({
      start: jest.fn((callback) => callback && callback()),
    })),
    Value: jest.fn(() => ({
      setValue: jest.fn(),
      interpolate: jest.fn(() => '0%'),
    })),
  };
  return {
    ...RN,
    Animated: mockAnimated,
  };
});

describe('BottomSheetLayout', () => {
  const mockOnRequestClose = jest.fn();
  const mockOnCloseButtonPressed = jest.fn();
  const mockOnBack = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const defaultProps = {
    title: 'Test Title',
    content: <AppText>Test Content</AppText>,
    onRequestClose: mockOnRequestClose,
    onCloseButtonPressed: mockOnCloseButtonPressed,
  };

  it('renders correctly with default props', () => {
    const { getByText, getByTestId } = render(<BottomSheetLayout {...defaultProps} />);

    expect(getByText('Test Title')).toBeTruthy();
    expect(getByText('Test Content')).toBeTruthy();
    expect(getByTestId('close-bottom-sheet-button')).toBeTruthy();
  });

  it('renders back button when onBack is provided', () => {
    const { queryByTestId } = render(<BottomSheetLayout {...defaultProps} onBack={mockOnBack} />);
    
    // Back button doesn't have a testID, but we can check if the component structure is correct
    expect(queryByTestId('close-bottom-sheet-button')).toBeTruthy();
  });

  it('calls animation on mount when enableAnimation is true', () => {
    const { Animated } = require('react-native');

    render(<BottomSheetLayout {...defaultProps} enableAnimation={true} />);

    // Should call parallel animation with both translateY and backdropOpacity
    expect(Animated.parallel).toHaveBeenCalled();
    expect(Animated.timing).toHaveBeenCalled();
  });

  it('does not call animation on mount when enableAnimation is false', () => {
    const { Animated } = require('react-native');
    jest.clearAllMocks();
    
    render(<BottomSheetLayout {...defaultProps} enableAnimation={false} />);
    
    expect(Animated.timing).not.toHaveBeenCalled();
  });

  it('handles close button press with animation enabled', async () => {
    const { getByTestId } = render(
      <BottomSheetLayout {...defaultProps} enableCloseAnimation={true} />
    );

    await act(async () => {
      fireEvent.press(getByTestId('close-bottom-sheet-button'));
    });

    expect(mockOnCloseButtonPressed).toHaveBeenCalledTimes(1);
    // onRequestClose should be called after animation completes
    expect(mockOnRequestClose).toHaveBeenCalledTimes(1);
  });

  it('handles close button press with animation disabled', async () => {
    const { getByTestId } = render(
      <BottomSheetLayout {...defaultProps} enableCloseAnimation={false} />
    );

    await act(async () => {
      fireEvent.press(getByTestId('close-bottom-sheet-button'));
    });

    expect(mockOnCloseButtonPressed).toHaveBeenCalledTimes(1);
    expect(mockOnRequestClose).toHaveBeenCalledTimes(1);
  });

  it('uses custom animation duration and initial transform', () => {
    const { Animated } = require('react-native');
    
    render(
      <BottomSheetLayout 
        {...defaultProps} 
        animationDuration={1000}
        initialTransform={200}
        enableAnimation={true}
      />
    );
    
    expect(Animated.timing).toHaveBeenCalledWith(
      expect.anything(),
      expect.objectContaining({
        duration: 1000,
        toValue: 0,
      })
    );
  });

  it('renders without title when title is not provided', () => {
    const { queryByText } = render(
      <BottomSheetLayout 
        content={<AppText>Test Content</AppText>}
        onRequestClose={mockOnRequestClose}
      />
    );

    expect(queryByText('Test Title')).toBeFalsy();
    expect(queryByText('Test Content')).toBeTruthy();
  });
});
