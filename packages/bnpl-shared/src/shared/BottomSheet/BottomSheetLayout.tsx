import React, { FC, ReactElement, ReactNode, useEffect, useRef, useState } from 'react';
import { Animated, Easing, KeyboardAvoidingView, Platform, TouchableOpacity, View, ViewStyle } from 'react-native';
import { StyleSheet } from '../styles/StyleSheet';
import { AppImage, AppText } from '../react-native-customized';
import { Colors } from '../styles/StyleUtils';
import { useKeyboard } from '../hook/useKeyboard';
import { CloseButton } from '../CloseButton';

type Props = {
  title?: string;
  content?: ReactElement | ReactNode | string | null;
  titleSize?: number;
  containerStyle?: ViewStyle;
  bodyStyle?: ViewStyle;
  closeIconSize?: number;
  onRequestClose?: () => void;
  onCloseButtonPressed?: () => void;
  onBack?: () => void;
  allowFullModal?: boolean;
  animationDuration?: number;
  initialTransform?: number;
  enableAnimation?: boolean;
  enableCloseAnimation?: boolean;
};
export const BottomSheetLayout: FC<Props> = ({
  title,
  content,
  containerStyle,
  bodyStyle,
  titleSize = 16,
  closeIconSize = 16,
  onRequestClose,
  onBack,
  allowFullModal = false,
  onCloseButtonPressed,
  animationDuration = 500,
  initialTransform = 100,
  enableAnimation = true,
  enableCloseAnimation = true,
}) => {
  const { hasKeyboard, bodyHeight } = useKeyboard();
  const [isClosing, setIsClosing] = useState(false);

  // Animation setup
  const translateY = useRef(new Animated.Value(enableAnimation ? initialTransform : 0)).current;
  const backdropOpacity = useRef(new Animated.Value(enableAnimation ? 0 : 0.7)).current;

  // Custom easing function matching cubic-bezier(0.32, 0.72, 0, 1)
  const customEasing = Easing.bezier(0.32, 0.72, 0, 1);

  // Slide in animation on mount
  useEffect(() => {
    if (enableAnimation && !isClosing) {
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: 0,
          duration: animationDuration,
          easing: customEasing,
          useNativeDriver: Platform.OS !== 'web',
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0.7,
          duration: animationDuration,
          easing: customEasing,
          useNativeDriver: Platform.OS !== 'web',
        }),
      ]).start();
    }
  }, [enableAnimation, animationDuration, customEasing, translateY, backdropOpacity, isClosing]);

  // Slide out animation when closing
  useEffect(() => {
    if (isClosing && enableCloseAnimation) {
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: initialTransform,
          duration: animationDuration,
          easing: customEasing,
          useNativeDriver: Platform.OS !== 'web',
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: animationDuration,
          easing: customEasing,
          useNativeDriver: Platform.OS !== 'web',
        }),
      ]).start(() => {
        // Call the actual close handlers after animation completes
        onRequestClose?.();
      });
    } else if (isClosing && !enableCloseAnimation) {
      // If close animation is disabled, close immediately
      onRequestClose?.();
    }
  }, [isClosing, enableCloseAnimation, initialTransform, animationDuration, customEasing, translateY, backdropOpacity, onRequestClose]);

  const handleCloseButtonPressed = () => {
    // Always call onCloseButtonPressed immediately for any custom logic
    onCloseButtonPressed?.();

    if (enableCloseAnimation) {
      setIsClosing(true);
    } else {
      onRequestClose?.();
    }
  };

  const handleBackPressed = () => {
    if (enableCloseAnimation && onBack) {
      setIsClosing(true);
      // Note: We need to handle onBack differently since it's not a close action
      // For now, let's treat back as immediate action
      onBack?.();
    } else {
      onBack?.();
    }
  };
  return (
    <Animated.View
      style={[
        styles.root,
        enableAnimation && {
          backgroundColor: backdropOpacity.interpolate({
            inputRange: [0, 0.7],
            outputRange: ['rgba(0, 0, 0, 0.0)', 'rgba(0, 0, 0, 0.7)'],
            extrapolate: 'clamp',
          }),
        },
      ]}
    >
      <KeyboardAvoidingView enabled behavior={Platform.OS === 'android' ? undefined : 'padding'} style={styles.keyboardAvoidingView}>
        <View style={[styles.container, containerStyle]}>
        <Animated.View
          style={[
            styles.body,
            { height: allowFullModal && hasKeyboard ? bodyHeight : 'auto' },
            bodyStyle,
            enableAnimation && {
              transform: [
                {
                  translateY: translateY.interpolate({
                    inputRange: [0, 100],
                    outputRange: ['0%', '100%'],
                    extrapolate: 'clamp',
                  }),
                },
              ],
            },
          ]}
        >
          <View style={[styles.titleContainer, !title && styles.titleContainerEmpty]}>
            {onBack && (
              <TouchableOpacity onPress={handleBackPressed} style={styles.backButton}>
                <AppImage source={require('./backImage.png')} height={closeIconSize} width={closeIconSize} />
              </TouchableOpacity>
            )}
            <AppText size={titleSize} height={closeIconSize + 4} weight="700" style={styles.title}>
              {title}
            </AppText>
            {onRequestClose && (
              <CloseButton
                testID="close-bottom-sheet-button"
                onPress={handleCloseButtonPressed}
                style={styles.closeButton}
                size={closeIconSize}
              />
            )}
          </View>

          {content}
        </Animated.View>
        </View>
      </KeyboardAvoidingView>
    </Animated.View>
  );
};

//#region
const styles = StyleSheet.create({
  root: {
    flex: 1,
    padding: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)', // Fallback for when animation is disabled
    flexDirection: 'column-reverse',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  titleContainerEmpty: {
    borderBottomWidth: 0,
  },
  body: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    backgroundColor: Colors.background,
  },
  title: {
    textAlign: 'center',
    marginVertical: 14,
  },
  titleContainer: {
    borderBottomWidth: 1,
    borderColor: '#F2F6F7',
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    padding: 8,
    top: 8,
    right: 8,
  },
  backButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    zIndex: 1,
  },
  container: { justifyContent: 'flex-end' },
});
//#endregion
