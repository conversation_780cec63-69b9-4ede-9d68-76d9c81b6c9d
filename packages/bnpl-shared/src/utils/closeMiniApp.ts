import { store } from '../redux/store';
import { UtmCampaign } from '../types';
import {
  EmbeddedOnboardingStatus,
  onSendEmbeddedOnboardingDataToCashier,
} from './onSendEmbeddedOnboardingDataToCashier';

/**
 * Closes the mini application based on the current context
 *
 * If the app is in embedded onboarding mode (determined by UTM campaign),
 * it sends a cancel status to the cashier.
 * Otherwise, it closes the window using the ZaloPay SDK.
 *
 * @returns {void}
 */

export const closeMiniApp = (): void => {
  const isEmbeddedOnboarding = store.getState().utm.utmCampaign === UtmCampaign.EmbeddedOnboarding;
  if (isEmbeddedOnboarding) {
    onSendEmbeddedOnboardingDataToCashier(EmbeddedOnboardingStatus.Cancel);
  } else {
    window?.zlpSdk?.UI?.closeWindow();
  }
};
