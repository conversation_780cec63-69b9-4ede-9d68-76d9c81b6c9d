import { createSubject, Observer, Subscription, Subject } from './observer';

describe('Observer Utils', () => {
  describe('createSubject', () => {
    it('should create a subject with initial value and empty observers array', () => {
      const initialValue = 'initial';
      const subject = createSubject<string>(initialValue);
      
      expect(subject.state).toBe(initialValue);
      expect(subject.observers).toEqual([]);
      expect(subject.observers.length).toBe(0);
    });

    it('should create a subject with different data types', () => {
      const numberSubject = createSubject<number>(42);
      const objectSubject = createSubject<{ id: number; name: string }>({ id: 1, name: 'test' });
      const booleanSubject = createSubject<boolean>(true);
      
      expect(numberSubject.state).toBe(42);
      expect(objectSubject.state).toEqual({ id: 1, name: 'test' });
      expect(booleanSubject.state).toBe(true);
    });

    it('should add observer when subscribe is called', () => {
      const subject = createSubject<string>('initial');
      const observer: Observer<string> = {
        next: jest.fn(),
      };

      subject.subscribe(observer);

      expect(subject.observers).toContain(observer);
      expect(subject.observers.length).toBe(1);
    });

    it('should immediately call observer.next with current state when subscribing', () => {
      const initialValue = 'initial-state';
      const subject = createSubject<string>(initialValue);
      const observer: Observer<string> = {
        next: jest.fn(),
      };

      subject.subscribe(observer);

      expect(observer.next).toHaveBeenCalledWith(initialValue);
      expect(observer.next).toHaveBeenCalledTimes(1);
    });

    it('should add multiple observers when subscribe is called multiple times', () => {
      const subject = createSubject<string>('initial');
      const observer1: Observer<string> = { next: jest.fn() };
      const observer2: Observer<string> = { next: jest.fn() };

      subject.subscribe(observer1);
      subject.subscribe(observer2);

      expect(subject.observers).toContain(observer1);
      expect(subject.observers).toContain(observer2);
      expect(subject.observers.length).toBe(2);
    });

    it('should call next on all observers with current state when they subscribe', () => {
      const initialValue = 'initial-state';
      const subject = createSubject<string>(initialValue);
      const observer1: Observer<string> = { next: jest.fn() };
      const observer2: Observer<string> = { next: jest.fn() };

      subject.subscribe(observer1);
      subject.subscribe(observer2);

      expect(observer1.next).toHaveBeenCalledWith(initialValue);
      expect(observer2.next).toHaveBeenCalledWith(initialValue);
      expect(observer1.next).toHaveBeenCalledTimes(1);
      expect(observer2.next).toHaveBeenCalledTimes(1);
    });

    it('should return subscription object with unsubscribe method', () => {
      const subject = createSubject<string>('initial');
      const observer: Observer<string> = { next: jest.fn() };

      const subscription = subject.subscribe(observer);

      expect(subscription).toBeDefined();
      expect(subscription.unsubscribe).toBeDefined();
      expect(typeof subscription.unsubscribe).toBe('function');
    });

    it('should call next on all observers when subject.next is called', () => {
      const subject = createSubject<string>('initial');
      const observer1: Observer<string> = { next: jest.fn() };
      const observer2: Observer<string> = { next: jest.fn() };
      const testValue = 'test-value';

      subject.subscribe(observer1);
      subject.subscribe(observer2);
      
      // Clear previous calls from subscription
      jest.clearAllMocks();
      
      subject.next(testValue);

      expect(observer1.next).toHaveBeenCalledWith(testValue);
      expect(observer2.next).toHaveBeenCalledWith(testValue);
      expect(observer1.next).toHaveBeenCalledTimes(1);
      expect(observer2.next).toHaveBeenCalledTimes(1);
    });

    it('should not call next on observers when subject.next is called with no observers', () => {
      const subject = createSubject<string>('initial');
      
      // Should not throw error when calling next with no observers
      expect(() => subject.next('test')).not.toThrow();
    });

    it('should remove observer when subscription.unsubscribe is called', () => {
      const subject = createSubject<string>('initial');
      const observer: Observer<string> = { next: jest.fn() };

      const subscription = subject.subscribe(observer);
      expect(subject.observers).toContain(observer);

      subscription.unsubscribe();
      expect(subject.observers).not.toContain(observer);
      expect(subject.observers.length).toBe(0);
    });

    it('should remove only the specific observer when subscription.unsubscribe is called', () => {
      const subject = createSubject<string>('initial');
      const observer1: Observer<string> = { next: jest.fn() };
      const observer2: Observer<string> = { next: jest.fn() };

      const subscription1 = subject.subscribe(observer1);
      const subscription2 = subject.subscribe(observer2);

      subscription1.unsubscribe();

      expect(subject.observers).not.toContain(observer1);
      expect(subject.observers).toContain(observer2);
      expect(subject.observers.length).toBe(1);
    });

    it('should not call next on unsubscribed observer', () => {
      const subject = createSubject<string>('initial');
      const observer1: Observer<string> = { next: jest.fn() };
      const observer2: Observer<string> = { next: jest.fn() };

      const subscription1 = subject.subscribe(observer1);
      subject.subscribe(observer2);

      subscription1.unsubscribe();
      
      // Clear previous calls from subscription
      jest.clearAllMocks();
      
      subject.next('test-value');

      expect(observer1.next).not.toHaveBeenCalled();
      expect(observer2.next).toHaveBeenCalledWith('test-value');
    });

    it('should remove all observers when subject.unsubscribe is called', () => {
      const subject = createSubject<string>('initial');
      const observer1: Observer<string> = { next: jest.fn() };
      const observer2: Observer<string> = { next: jest.fn() };

      subject.subscribe(observer1);
      subject.subscribe(observer2);
      expect(subject.observers.length).toBe(2);

      subject.unsubscribe();
      expect(subject.observers.length).toBe(0);
      expect(subject.observers).toEqual([]);
    });

    it('should not call next on any observer after subject.unsubscribe', () => {
      const subject = createSubject<string>('initial');
      const observer1: Observer<string> = { next: jest.fn() };
      const observer2: Observer<string> = { next: jest.fn() };

      subject.subscribe(observer1);
      subject.subscribe(observer2);
      subject.unsubscribe();
      
      // Clear previous calls from subscription
      jest.clearAllMocks();
      
      subject.next('test-value');

      expect(observer1.next).not.toHaveBeenCalled();
      expect(observer2.next).not.toHaveBeenCalled();
    });

    it('should handle observer with undefined next method gracefully in subject.next', () => {
      const subject = createSubject<string>('initial');
      const observer: Observer<string> = { next: jest.fn() };

      // Subscribe normally first
      subject.subscribe(observer);
      
      // Then simulate undefined next
      observer.next = undefined as any;
      
      // subject.next should not throw error even with undefined next
      expect(() => subject.next('test-value')).not.toThrow();
    });

    it('should handle observer with null next method gracefully in subject.next', () => {
      const subject = createSubject<string>('initial');
      const observer: Observer<string> = { next: jest.fn() };

      // Subscribe normally first
      subject.subscribe(observer);
      
      // Then simulate null next
      observer.next = null as any;
      
      // subject.next should not throw error even with null next
      expect(() => subject.next('test-value')).not.toThrow();
    });

    it('should work with different data types', () => {
      // Test with number
      const numberSubject = createSubject<number>(0);
      const numberObserver: Observer<number> = { next: jest.fn() };
      numberSubject.subscribe(numberObserver);
      
      // Clear subscription call
      jest.clearAllMocks();
      
      numberSubject.next(42);
      expect(numberObserver.next).toHaveBeenCalledWith(42);

      // Test with object
      const objectSubject = createSubject<{ id: number; name: string }>({ id: 0, name: 'initial' });
      const objectObserver: Observer<{ id: number; name: string }> = { next: jest.fn() };
      objectSubject.subscribe(objectObserver);
      
      // Clear subscription call
      jest.clearAllMocks();
      
      const testObject = { id: 1, name: 'test' };
      objectSubject.next(testObject);
      expect(objectObserver.next).toHaveBeenCalledWith(testObject);
    });

    it('should handle multiple subscription and unsubscription cycles', () => {
      const subject = createSubject<string>('initial');
      const observer: Observer<string> = { next: jest.fn() };

      // Subscribe and unsubscribe multiple times
      const subscription1 = subject.subscribe(observer);
      subscription1.unsubscribe();
      
      const subscription2 = subject.subscribe(observer);
      subscription2.unsubscribe();
      
      const subscription3 = subject.subscribe(observer);
      
      // Clear subscription calls
      jest.clearAllMocks();
      
      subject.next('test-value');

      expect(observer.next).toHaveBeenCalledWith('test-value');
      expect(observer.next).toHaveBeenCalledTimes(1);
      expect(subject.observers.length).toBe(1);
    });

    it('should handle unsubscribing the same subscription multiple times', () => {
      const subject = createSubject<string>('initial');
      const observer: Observer<string> = { next: jest.fn() };

      const subscription = subject.subscribe(observer);
      subscription.unsubscribe();
      
      expect(() => subscription.unsubscribe()).not.toThrow();
      expect(subject.observers.length).toBe(0);
    });

    it('should maintain observer order after partial unsubscribe', () => {
      const subject = createSubject<string>('initial');
      const observer1: Observer<string> = { next: jest.fn() };
      const observer2: Observer<string> = { next: jest.fn() };
      const observer3: Observer<string> = { next: jest.fn() };

      subject.subscribe(observer1);
      const subscription2 = subject.subscribe(observer2);
      subject.subscribe(observer3);

      subscription2.unsubscribe();

      expect(subject.observers).toEqual([observer1, observer3]);
      expect(subject.observers.length).toBe(2);
    });

    it('should maintain state correctly throughout lifecycle', () => {
      const initialValue = 'initial';
      const subject = createSubject<string>(initialValue);
      
      expect(subject.state).toBe(initialValue);
      
      // State should not change when subscribing/unsubscribing
      const observer: Observer<string> = { next: jest.fn() };
      subject.subscribe(observer);
      expect(subject.state).toBe(initialValue);
      
      subject.unsubscribe();
      expect(subject.state).toBe(initialValue);
    });

    it('should handle empty string and falsy values correctly', () => {
      const emptyStringSubject = createSubject<string>('');
      const zeroSubject = createSubject<number>(0);
      const falseSubject = createSubject<boolean>(false);
      const nullSubject = createSubject<string | null>(null);

      expect(emptyStringSubject.state).toBe('');
      expect(zeroSubject.state).toBe(0);
      expect(falseSubject.state).toBe(false);
      expect(nullSubject.state).toBe(null);
    });

    it('should handle complex object updates', () => {
      const initialUser = { id: 1, name: 'John', email: '<EMAIL>' };
      const subject = createSubject<typeof initialUser>(initialUser);
      const observer: Observer<typeof initialUser> = { next: jest.fn() };

      subject.subscribe(observer);
      
      // Clear subscription call
      jest.clearAllMocks();

      const updatedUser = { id: 1, name: 'John Updated', email: '<EMAIL>' };
      subject.next(updatedUser);

      expect(observer.next).toHaveBeenCalledWith(updatedUser);
    });
  });
}); 
