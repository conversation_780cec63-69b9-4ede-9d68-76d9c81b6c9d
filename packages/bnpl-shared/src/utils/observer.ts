export function createSubject<T>(initialValue: T): Subject<T> {
  let state: T = initialValue;
  let _observers: Observer<T>[] = []

  const next = (value: T) => {
    state = value;
    for (const observer of _observers) {
      observer?.next?.(value)
    }
  }

  const subscribe = (observer: Observer<T>): Subscription => {
    _observers.push(observer)
    observer?.next?.(state)
    return {
      unsubscribe: () => {
        _observers = _observers.filter((o) => o !== observer)
      },
    }
  }

  const unsubscribe = () => {
    _observers = []
  }

  return {
    get observers() {
      return [..._observers]
    },
    get state() {
      return state
    },
    next,
    subscribe,
    unsubscribe,
  }
}

export type Observer<T> = {
  next: (value: T) => void
}

export type Subscription = {
  unsubscribe: () => void
}

export type Subject<T> = {
  readonly state: T
  readonly observers: Observer<T>[]
  subscribe: (observer: Observer<T>) => Subscription
  unsubscribe: () => void
} & Observer<T>
