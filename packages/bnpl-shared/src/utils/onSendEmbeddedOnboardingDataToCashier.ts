import { closeWebview, sendDataToPreviousMiniApp } from '@zpi/js-bridge';
import { getAppInfo } from 'bnpl-shared/src/lib/ZalopaySDK';
import { PLATFORM } from 'bnpl-shared/src/constants';

export const EmbeddedOnboardingStatus = {
  Approved: 'approved',
  Rejected: 'rejected',
  Timeout: 'timeout',
  Cancel: 'cancel',
};

const STATUS_MAPPING = {
  [EmbeddedOnboardingStatus.Approved]: { status: 'complete', message: 'approved' },
  [EmbeddedOnboardingStatus.Rejected]: { status: 'failed', message: 'rejected' },
  [EmbeddedOnboardingStatus.Timeout]: { status: 'cancel', message: 'timeout' },
  [EmbeddedOnboardingStatus.Cancel]: { status: 'cancel', message: 'user cancel' },
} as const;

const POST_MESSAGE_EVENT = 'paylater_embbeded_onboarding';

export const onSendEmbeddedOnboardingDataToCashier = async (status: string) => {
  const { platform } = (await getAppInfo()).data;
  try {
    // send data to ZPA
    if (platform === PLATFORM.ZPA) {
      if (status === EmbeddedOnboardingStatus.Approved) {
        window?.zlpSdk?.Payment?.onPrePaymentComplete?.();
      }
      if ([EmbeddedOnboardingStatus.Rejected, EmbeddedOnboardingStatus.Timeout].includes(status)) {
        window?.zlpSdk?.Payment?.onPrePaymentFailed?.();
      }
      if (status === EmbeddedOnboardingStatus.Cancel) {
        window?.zlpSdk?.Payment?.onPrePaymentCancel?.();
      }
    }

    const callbackValue = JSON.stringify(STATUS_MAPPING[status] ?? {});

    // Send data to the parent window (for ZPI)
    window?.parent?.postMessage({
      event: POST_MESSAGE_EVENT,
      data: callbackValue,
    });

    // send data to the previous mini app (for ZMP)
    sendDataToPreviousMiniApp(callbackValue, _cb => {
      closeWebview();
    });
  } catch (error) {
    console.error('Error sending embedded onboarding data to cashier:', error);
  }
};
