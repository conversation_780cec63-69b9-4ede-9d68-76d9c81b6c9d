import { openPhone } from './openPhone';
import { isZPAPlatform } from './detectPlatform';
import { asMock } from '../jest/utils';

// Mock the detectPlatform module
jest.mock('./detectPlatform', () => ({
  isZPAPlatform: jest.fn(),
}));

// Mock window object
const mockZlpSdkOpenPhone = jest.fn();
const mockWindowOpen = jest.fn();

// Setup window object with zlpSdk
Object.defineProperty(window, 'zlpSdk', {
  writable: true,
  value: {
    Device: {
      openPhone: mockZlpSdkOpenPhone,
    },
  },
});

// Setup window.open
Object.defineProperty(window, 'open', {
  writable: true,
  value: mockWindowOpen,
});

describe('openPhone', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when isZPAPlatform returns true', () => {
    it('should call window.zlpSdk.Device.openPhone with correct phone number', () => {
      // Arrange
      const testPhone = '0123456789';
      asMock(isZPAPlatform).mockReturnValue(true);

      // Act
      openPhone(testPhone);

      // Assert
      expect(isZPAPlatform).toHaveBeenCalledTimes(1);
      expect(mockZlpSdkOpenPhone).toHaveBeenCalledWith({ phone: testPhone });
      expect(mockZlpSdkOpenPhone).toHaveBeenCalledTimes(1);
      expect(mockWindowOpen).not.toHaveBeenCalled();
    });

    it('should handle empty phone number for ZPA platform', () => {
      // Arrange
      const testPhone = '';
      asMock(isZPAPlatform).mockReturnValue(true);

      // Act
      openPhone(testPhone);

      // Assert
      expect(isZPAPlatform).toHaveBeenCalledTimes(1);
      expect(mockZlpSdkOpenPhone).toHaveBeenCalledWith({ phone: testPhone });
      expect(mockZlpSdkOpenPhone).toHaveBeenCalledTimes(1);
      expect(mockWindowOpen).not.toHaveBeenCalled();
    });

    it('should handle phone number with special characters', () => {
      // Arrange
      const testPhone = '+84-123-456-789';
      asMock(isZPAPlatform).mockReturnValue(true);

      // Act
      openPhone(testPhone);

      // Assert
      expect(isZPAPlatform).toHaveBeenCalledTimes(1);
      expect(mockZlpSdkOpenPhone).toHaveBeenCalledWith({ phone: testPhone });
      expect(mockZlpSdkOpenPhone).toHaveBeenCalledTimes(1);
      expect(mockWindowOpen).not.toHaveBeenCalled();
    });
  });

  describe('when isZPAPlatform returns false', () => {
    it('should call window.open with tel: protocol for non-ZPA platform', () => {
      // Arrange
      const testPhone = '0123456789';
      asMock(isZPAPlatform).mockReturnValue(false);

      // Act
      openPhone(testPhone);

      // Assert
      expect(isZPAPlatform).toHaveBeenCalledTimes(1);
      expect(mockWindowOpen).toHaveBeenCalledWith(`tel:${testPhone}`);
      expect(mockWindowOpen).toHaveBeenCalledTimes(1);
      expect(mockZlpSdkOpenPhone).not.toHaveBeenCalled();
    });

    it('should handle empty phone number for non-ZPA platform', () => {
      // Arrange
      const testPhone = '';
      asMock(isZPAPlatform).mockReturnValue(false);

      // Act
      openPhone(testPhone);

      // Assert
      expect(isZPAPlatform).toHaveBeenCalledTimes(1);
      expect(mockWindowOpen).toHaveBeenCalledWith('tel:');
      expect(mockWindowOpen).toHaveBeenCalledTimes(1);
      expect(mockZlpSdkOpenPhone).not.toHaveBeenCalled();
    });

    it('should handle long phone number', () => {
      // Arrange
      const testPhone = '01234567890123456789';
      asMock(isZPAPlatform).mockReturnValue(false);

      // Act
      openPhone(testPhone);

      // Assert
      expect(isZPAPlatform).toHaveBeenCalledTimes(1);
      expect(mockWindowOpen).toHaveBeenCalledWith(`tel:${testPhone}`);
      expect(mockWindowOpen).toHaveBeenCalledTimes(1);
      expect(mockZlpSdkOpenPhone).not.toHaveBeenCalled();
    });
  });

  describe('edge cases', () => {
    it('should work correctly when called multiple times', () => {
      // Arrange
      const testPhone1 = '0123456789';
      const testPhone2 = '0987654321';
      asMock(isZPAPlatform).mockReturnValue(true);

      // Act
      openPhone(testPhone1);
      openPhone(testPhone2);

      // Assert
      expect(isZPAPlatform).toHaveBeenCalledTimes(2);
      expect(mockZlpSdkOpenPhone).toHaveBeenCalledTimes(2);
      expect(mockZlpSdkOpenPhone).toHaveBeenNthCalledWith(1, { phone: testPhone1 });
      expect(mockZlpSdkOpenPhone).toHaveBeenNthCalledWith(2, { phone: testPhone2 });
      expect(mockWindowOpen).not.toHaveBeenCalled();
    });

    it('should switch behavior when platform detection changes', () => {
      // Arrange
      const testPhone = '0123456789';

      // First call - ZPA platform
      asMock(isZPAPlatform).mockReturnValue(true);
      openPhone(testPhone);

      // Second call - non-ZPA platform
      asMock(isZPAPlatform).mockReturnValue(false);
      openPhone(testPhone);

      // Assert
      expect(isZPAPlatform).toHaveBeenCalledTimes(2);
      expect(mockZlpSdkOpenPhone).toHaveBeenCalledTimes(1);
      expect(mockZlpSdkOpenPhone).toHaveBeenCalledWith({ phone: testPhone });
      expect(mockWindowOpen).toHaveBeenCalledTimes(1);
      expect(mockWindowOpen).toHaveBeenCalledWith(`tel:${testPhone}`);
    });
  });
});
